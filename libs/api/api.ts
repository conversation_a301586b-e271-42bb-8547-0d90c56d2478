/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export interface ChannelMembersOutputDto {
  avatarUrl?: string | null;
  /** @format date-time */
  birthday: string;
  email: string;
  id: string;
  isMuted: boolean;
  /** @format date-time */
  lastActive: string | null;
  name: string;
}

export interface ChannelOutputDto {
  description?: string | null;
  id: string;
  is_private: boolean;
  /** @format int64 */
  members_count: number;
  /**
   * @minLength 1
   * @maxLength 100000
   */
  name: string;
  workspace_id: string;
}

export interface CreateChannelOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  description?: string | null;
  is_private?: boolean;
  /**
   * @minLength 1
   * @maxLength 100000
   */
  name: string;
  workspace_id: string;
}

export interface ErrorDetail {
  /** Where the error occurred, e.g. 'body.items[3].tags' or 'path.thing-id' */
  location?: string;
  /** Error message text */
  message?: string;
  /** The value at the given location */
  value?: any;
}

export interface ErrorModel {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  /** A human-readable explanation specific to this occurrence of the problem. */
  detail?: string;
  /** Optional list of individual error details */
  errors?: ErrorDetail[];
  /**
   * A URI reference that identifies the specific occurrence of the problem.
   * @format uri
   */
  instance?: string;
  /**
   * HTTP status code
   * @format int64
   */
  status?: number;
  /** A short, human-readable summary of the problem type. This value should not change between occurrences of the error. */
  title?: string;
  /**
   * A URI reference to human-readable documentation for the error.
   * @format uri
   * @default "about:blank"
   */
  type?: string;
}

export interface GetChannelMembersOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  items: ChannelMembersOutputDto[];
  /** @format int64 */
  total: number;
}

export interface GetChannelsOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  items: ChannelOutputDto[];
  /** @format int64 */
  total: number;
}

export interface GetMessagesOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  items: MessageOutputDto[];
  /** @format int64 */
  total: number;
}

export interface GetWorkspaceMembersOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  items: WorkspaceMembersOutputDto[];
  /** @format int64 */
  total: number;
}

export interface GetWorkspacesOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  items: WorkspaceOutputDto[];
  /** @format int64 */
  total: number;
}

export interface LoginInputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  /**
   * @minLength 1
   * @maxLength 100
   */
  email: string;
  /**
   * @minLength 1
   * @maxLength 100
   */
  password: string;
}

export interface LoginOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  userID: string;
}

export interface LogoutOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  success: boolean;
}

export interface MeOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  data: User;
}

export interface MessageOutputDto {
  body: string;
  channelId: string | null;
  conversationId: string | null;
  /** @format date-time */
  createdAt: string;
  id: string;
  senderAvatar: string;
  senderId: string;
  senderName: string;
  type: string;
  /** @format date-time */
  updatedAt: string;
  workspaceId: string;
}

export interface RegisterInputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  /** @format date-time */
  birthday: string;
  /**
   * @format email
   * @minLength 1
   * @maxLength 100
   */
  email: string;
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /**
   * @minLength 8
   * @maxLength 128
   */
  password: string;
}

export interface RegisterOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  data: User;
}

export interface SendWorkspaceInviteRequest {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  /**
   * @format email
   * @maxItems 10
   * @minItems 1
   * @uniqueItems true
   */
  emails: string[];
  workspace_id: string;
}

export interface SendInviteOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  existing: string[];
  failed: string[];
  sent: string[];
}

export interface SendMessageInputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  /** @minLength 1 */
  body: string;
  channelId?: string | null;
  conversationId?: string | null;
  type: SendMessageInputDtoTypeEnum;
  workspaceId: string;
}

export interface SendMessageOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  body: string;
  channel_id: string | null;
  conversation_id: string | null;
  workspace_id: string;
}

export interface UpdateWorkspaceRequest {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  description?: string | null;
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
}

export interface User {
  avatarURL: string;
  /** @format date-time */
  birthday: string;
  /** @format date-time */
  created_at: string;
  email: string;
  emailVerified: boolean;
  id: string;
  name: string;
  /** @format date-time */
  updated_at: string;
}

export interface VerifyEmailOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  messages: string;
}

export interface WorkspaceMembersOutputDto {
  invited_by: string;
  /** @format date-time */
  last_active: string;
  role: WorkspaceMembersOutputDtoRoleEnum;
  user_avatar_url: string;
  user_email: string;
  user_id: string;
  user_name: string;
  workspace_id: string;
}

export interface WorkspaceOutputDto {
  /**
   * A URL to the JSON Schema for this object.
   * @format uri
   */
  $schema?: string;
  description: string;
  iconUrl: string;
  id: string;
  name: string;
}

export enum SendMessageInputDtoTypeEnum {
  Text = "text",
  Image = "image",
  File = "file",
}

export enum WorkspaceMembersOutputDtoRoleEnum {
  Owner = "owner",
  Admin = "admin",
  Member = "member",
}

export type QueryParamsType = Record<string | number, any>;
export type ResponseFormat = keyof Omit<Body, "body" | "bodyUsed">;

export interface FullRequestParams extends Omit<RequestInit, "body"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseFormat;
  /** request body */
  body?: unknown;
  /** base url */
  baseUrl?: string;
  /** request cancellation token */
  cancelToken?: CancelToken;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown> {
  baseUrl?: string;
  baseApiParams?: Omit<RequestParams, "baseUrl" | "cancelToken" | "signal">;
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<RequestParams | void> | RequestParams | void;
  customFetch?: typeof fetch;
}

export interface HttpResponse<D extends unknown, E extends unknown = unknown>
  extends Response {
  data: D;
  error: E;
}

type CancelToken = Symbol | string | number;

export enum ContentType {
  Json = "application/json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public baseUrl: string = "http://localhost:8080/";
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private abortControllers = new Map<CancelToken, AbortController>();
  private customFetch = (...fetchParams: Parameters<typeof fetch>) =>
    fetch(...fetchParams);

  private baseApiParams: RequestParams = {
    credentials: "same-origin",
    headers: {},
    redirect: "follow",
    referrerPolicy: "no-referrer",
  };

  constructor(apiConfig: ApiConfig<SecurityDataType> = {}) {
    Object.assign(this, apiConfig);
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected encodeQueryParam(key: string, value: any) {
    const encodedKey = encodeURIComponent(key);
    return `${encodedKey}=${encodeURIComponent(typeof value === "number" ? value : `${value}`)}`;
  }

  protected addQueryParam(query: QueryParamsType, key: string) {
    return this.encodeQueryParam(key, query[key]);
  }

  protected addArrayQueryParam(query: QueryParamsType, key: string) {
    const value = query[key];
    return value.map((v: any) => this.encodeQueryParam(key, v)).join("&");
  }

  protected toQueryString(rawQuery?: QueryParamsType): string {
    const query = rawQuery || {};
    const keys = Object.keys(query).filter(
      (key) => "undefined" !== typeof query[key],
    );
    return keys
      .map((key) =>
        Array.isArray(query[key])
          ? this.addArrayQueryParam(query, key)
          : this.addQueryParam(query, key),
      )
      .join("&");
  }

  protected addQueryParams(rawQuery?: QueryParamsType): string {
    const queryString = this.toQueryString(rawQuery);
    return queryString ? `?${queryString}` : "";
  }

  private contentFormatters: Record<ContentType, (input: any) => any> = {
    [ContentType.Json]: (input: any) =>
      input !== null && (typeof input === "object" || typeof input === "string")
        ? JSON.stringify(input)
        : input,
    [ContentType.Text]: (input: any) =>
      input !== null && typeof input !== "string"
        ? JSON.stringify(input)
        : input,
    [ContentType.FormData]: (input: any) =>
      Object.keys(input || {}).reduce((formData, key) => {
        const property = input[key];
        formData.append(
          key,
          property instanceof Blob
            ? property
            : typeof property === "object" && property !== null
              ? JSON.stringify(property)
              : `${property}`,
        );
        return formData;
      }, new FormData()),
    [ContentType.UrlEncoded]: (input: any) => this.toQueryString(input),
  };

  protected mergeRequestParams(
    params1: RequestParams,
    params2?: RequestParams,
  ): RequestParams {
    return {
      ...this.baseApiParams,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...(this.baseApiParams.headers || {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected createAbortSignal = (
    cancelToken: CancelToken,
  ): AbortSignal | undefined => {
    if (this.abortControllers.has(cancelToken)) {
      const abortController = this.abortControllers.get(cancelToken);
      if (abortController) {
        return abortController.signal;
      }
      return void 0;
    }

    const abortController = new AbortController();
    this.abortControllers.set(cancelToken, abortController);
    return abortController.signal;
  };

  public abortRequest = (cancelToken: CancelToken) => {
    const abortController = this.abortControllers.get(cancelToken);

    if (abortController) {
      abortController.abort();
      this.abortControllers.delete(cancelToken);
    }
  };

  public request = async <T = any, E = any>({
    body,
    secure,
    path,
    type,
    query,
    format,
    baseUrl,
    cancelToken,
    ...params
  }: FullRequestParams): Promise<HttpResponse<T, E>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.baseApiParams.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const queryString = query && this.toQueryString(query);
    const payloadFormatter = this.contentFormatters[type || ContentType.Json];
    const responseFormat = format || requestParams.format;

    return this.customFetch(
      `${baseUrl || this.baseUrl || ""}${path}${queryString ? `?${queryString}` : ""}`,
      {
        ...requestParams,
        headers: {
          ...(requestParams.headers || {}),
          ...(type && type !== ContentType.FormData
            ? { "Content-Type": type }
            : {}),
        },
        signal:
          (cancelToken
            ? this.createAbortSignal(cancelToken)
            : requestParams.signal) || null,
        body:
          typeof body === "undefined" || body === null
            ? null
            : payloadFormatter(body),
      },
    ).then(async (response) => {
      const r = response.clone() as HttpResponse<T, E>;
      r.data = null as unknown as T;
      r.error = null as unknown as E;

      const data = !responseFormat
        ? r
        : await response[responseFormat]()
            .then((data) => {
              if (r.ok) {
                r.data = data;
              } else {
                r.error = data;
              }
              return r;
            })
            .catch((e) => {
              r.error = e;
              return r;
            });

      if (cancelToken) {
        this.abortControllers.delete(cancelToken);
      }

      if (!response.ok) throw data;
      return data;
    });
  };
}

/**
 * @title Coorpe Api
 * @version 1.0.0
 * @baseUrl http://localhost:8080/
 */
export class Api<SecurityDataType extends unknown> {
  http: HttpClient<SecurityDataType>;

  constructor(http: HttpClient<SecurityDataType>) {
    this.http = http;
  }

  api = {
    /**
     * No description
     *
     * @tags Authentication
     * @name Login
     * @summary Login user
     * @request POST:/api/auth/login
     * @response `200` `LoginOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    login: (data: LoginInputDto, params: RequestParams = {}) =>
      this.http.request<LoginOutputDto, any>({
        path: `/api/auth/login`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Authentication
     * @name Logout
     * @summary Logout user
     * @request POST:/api/auth/logout
     * @secure
     * @response `200` `LogoutOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    logout: (params: RequestParams = {}) =>
      this.http.request<LogoutOutputDto, any>({
        path: `/api/auth/logout`,
        method: "POST",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Authentication
     * @name Me
     * @summary Get current user
     * @request GET:/api/auth/me
     * @secure
     * @response `200` `MeOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    me: (params: RequestParams = {}) =>
      this.http.request<MeOutputDto, any>({
        path: `/api/auth/me`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Authentication
     * @name Register
     * @summary Register user
     * @request POST:/api/auth/register
     * @response `200` `RegisterOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    register: (data: RegisterInputDto, params: RequestParams = {}) =>
      this.http.request<RegisterOutputDto, any>({
        path: `/api/auth/register`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Needs authentication
     *
     * @tags Channel
     * @name CreateChannel
     * @summary Create a channel
     * @request POST:/api/channel
     * @secure
     * @response `200` `CreateChannelOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    createChannel: (data: CreateChannelOutputDto, params: RequestParams = {}) =>
      this.http.request<CreateChannelOutputDto, any>({
        path: `/api/channel`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags ChannelMembers
     * @name GetChannelMembers
     * @summary Get channel members
     * @request GET:/api/channel/{channelId}/members
     * @response `200` `GetChannelMembersOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    getChannelMembers: (channelId: string, params: RequestParams = {}) =>
      this.http.request<GetChannelMembersOutputDto, any>({
        path: `/api/channel/${channelId}/members`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * @description Needs authentication
     *
     * @tags Channel
     * @name GetChannels
     * @summary Get all channels
     * @request GET:/api/channels/{workspaceId}
     * @secure
     * @response `200` `GetChannelsOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    getChannels: (
      workspaceId: string,
      query?: {
        /**
         * @format int64
         * @min 1
         * @default 1
         * @example 1
         */
        page?: number;
        /**
         * @format int64
         * @min 0
         * @default 20
         * @example 20
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.http.request<GetChannelsOutputDto, any>({
        path: `/api/channels/${workspaceId}`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Sends an email code to the provided address
     *
     * @tags Mail
     * @name SendEmailCode
     * @summary Send email code
     * @request POST:/api/mail/send-email-code
     * @response `200` `VerifyEmailOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    sendEmailCode: (
      query: {
        /**
         * @format email
         * @example "<EMAIL>"
         */
        email: string;
      },
      params: RequestParams = {},
    ) =>
      this.http.request<VerifyEmailOutputDto, any>({
        path: `/api/mail/send-email-code`,
        method: "POST",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Verifies an email code
     *
     * @tags Mail
     * @name VerifyEmailCode
     * @summary Verify email code
     * @request POST:/api/mail/verify-email-code
     * @response `200` `VerifyEmailOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    verifyEmailCode: (
      query: {
        /** @example "123456" */
        code: string;
      },
      params: RequestParams = {},
    ) =>
      this.http.request<VerifyEmailOutputDto, any>({
        path: `/api/mail/verify-email-code`,
        method: "POST",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Sends a channel message
     *
     * @tags Message
     * @name SendChannelMessages
     * @summary Send a channel message
     * @request POST:/api/messages
     * @secure
     * @response `200` `SendMessageOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    sendChannelMessages: (
      data: SendMessageInputDto,
      params: RequestParams = {},
    ) =>
      this.http.request<SendMessageOutputDto, any>({
        path: `/api/messages`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieves messages from a specific channel with pagination
     *
     * @tags Message
     * @name GetChannelMessages
     * @summary Get channel messages
     * @request GET:/api/messages/channel/{channelId}
     * @secure
     * @response `200` `GetMessagesOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    getChannelMessages: (
      channelId: string,
      query?: {
        /**
         * @format int64
         * @min 1
         * @default 1
         * @example 1
         */
        page?: number;
        /**
         * @format int64
         * @min 0
         * @default 20
         * @example 20
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.http.request<GetMessagesOutputDto, any>({
        path: `/api/messages/channel/${channelId}`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieves all workspaces for the authenticated user
     *
     * @tags Workspace
     * @name GetAllWorkspaces
     * @summary Get all workspaces
     * @request GET:/api/workspaces
     * @secure
     * @response `200` `GetWorkspacesOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    getAllWorkspaces: (
      query?: {
        /**
         * @format int64
         * @min 1
         * @default 1
         * @example 1
         */
        page?: number;
        /**
         * @format int64
         * @min 0
         * @default 20
         * @example 20
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.http.request<GetWorkspacesOutputDto, any>({
        path: `/api/workspaces`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Creates a new workspace
     *
     * @tags Workspace
     * @name CreateWorkspace
     * @summary Create a workspace
     * @request POST:/api/workspaces
     * @secure
     * @response `200` `WorkspaceOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    createWorkspace: (
      data: {
        description?: string | null;
        /** @format binary */
        icon?: File;
        name?: string;
      },
      params: RequestParams = {},
    ) =>
      this.http.request<WorkspaceOutputDto, any>({
        path: `/api/workspaces`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.FormData,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieves a workspace by its id
     *
     * @tags Workspace
     * @name GetWorkspaceById
     * @summary Get workspace by id
     * @request GET:/api/workspaces/id/{id}
     * @response `200` `WorkspaceOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    getWorkspaceById: (id: string, params: RequestParams = {}) =>
      this.http.request<WorkspaceOutputDto, any>({
        path: `/api/workspaces/id/${id}`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * @description Sends an invitation to join a workspace
     *
     * @tags Workspace
     * @name SendWorkspaceInvite
     * @summary Send workspace invite
     * @request POST:/api/workspaces/invite
     * @secure
     * @response `200` `SendInviteOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    sendWorkspaceInvite: (
      data: SendWorkspaceInviteRequest,
      params: RequestParams = {},
    ) =>
      this.http.request<SendInviteOutputDto, any>({
        path: `/api/workspaces/invite`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Updates an existing workspace by ID
     *
     * @tags Workspace
     * @name UpdateWorkspace
     * @summary Update a workspace
     * @request PUT:/api/workspaces/{id}
     * @secure
     * @response `200` `WorkspaceOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    updateWorkspace: (
      id: string,
      data: UpdateWorkspaceRequest,
      params: RequestParams = {},
    ) =>
      this.http.request<WorkspaceOutputDto, any>({
        path: `/api/workspaces/${id}`,
        method: "PUT",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieves all members of a workspace
     *
     * @tags WorkspaceMembers
     * @name GetWorkspaceMembers
     * @summary Get workspace members
     * @request GET:/api/workspaces/{id}/members
     * @secure
     * @response `200` `GetWorkspaceMembersOutputDto` OK
     * @response `default` `ErrorModel` Error
     */
    getWorkspaceMembers: (id: string, params: RequestParams = {}) =>
      this.http.request<GetWorkspaceMembersOutputDto, any>({
        path: `/api/workspaces/${id}/members`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),
  };
}
