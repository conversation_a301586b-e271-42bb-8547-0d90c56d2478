package baseapp

import (
	"github.com/coorpe-app/coorpe/libs/bus"
	"github.com/coorpe-app/coorpe/libs/config"
	"github.com/coorpe-app/coorpe/libs/logger"
	"go.uber.org/fx"
)

func CreateBaseApp(appName string) fx.Option {
	return fx.Options(
		fx.Provide(
			bus.NewNatsBusFx(appName),
			config.New,
			logger.NewFx(
				logger.Opts{
					Service: appName,
				},
			),
			newRedis,
			NewGorm,
		),
		fx.Invoke(
			NewGorm,
		),
	)
}
