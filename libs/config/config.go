package config

import (
	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
	"os"
	"path/filepath"
)

type Config struct {
	AppEnv string `envconfig:"APP_ENV" default:"development"`

	PostgresURL string `envconfig:"POSTGRES_URL" default:"postgres://coorpe:coorpe@localhost:5432/coorpe?sslmode=disable"`
	NatsURL     string `envconfig:"NATS_URL" default:"nats://localhost:4222" required:"true"`
	RedisURL    string `envconfig:"REDIS_URL" default:"redis://localhost:6379"`

	ApiPort int `envconfig:"API_PORT" default:"8080" required:"false"`

	S3Host        string `envconfig:"S3_HOST"`
	S3AccessToken string `envconfig:"S3_ACCESS_TOKEN"`
	S3SecretToken string `envconfig:"S3_SECRET_TOKEN"`
	S3Region      string `envconfig:"S3_REGION"`
	S3Bucket      string `envconfig:"S3_BUCKET"`
	S3PublicUrl   string `envconfig:"S3_PUBLIC_URL"`

	GmailMail       string `envconfig:"GMAIL_MAIL"`
	GmailPassword   string `envconfig:"GMAIL_PASSWORD"`
	GmailServerHost string `envconfig:"GMAIL_SERVER_HOST"`
	GmailServerPort int    `envconfig:"GMAIL_SERVER_PORT"`
}

func New() (Config, error) {
	cfg := Config{}

	wd, err := os.Getwd()
	if err != nil {
		return cfg, err
	}

	envPath := filepath.Join(wd, ".env")

	_ = godotenv.Load(envPath)

	if err := envconfig.Process("", &cfg); err != nil {
		return cfg, err
	}

	return cfg, nil
}
