package main

import (
	"database/sql"
	"embed"
	"github.com/coorpe-app/coorpe/libs/config"
	"github.com/lib/pq"
	"github.com/pressly/goose/v3"
)

//go:embed migrations/*.sql
var embedMigrations embed.FS

const driver = "postgres"

func main() {
	cfg, err := config.New()
	if err != nil {
		panic(err)
	}

	opts, err := pq.ParseURL(cfg.PostgresURL)
	if err != nil {
		panic(err)
	}

	db, err := sql.Open(driver, opts)

	goose.SetBaseFS(embedMigrations)

	if err := goose.SetDialect(driver); err != nil {
		panic(err)
	}

	if err := goose.Up(db, "migrations", goose.WithAllowMissing()); err != nil {
		panic(err)
	}
}
