package bus

import (
	kanban_boards "github.com/coorpe-app/coorpe/libs/bus/boards"
	"github.com/coorpe-app/coorpe/libs/bus/channel-messages"
	kanban_columns "github.com/coorpe-app/coorpe/libs/bus/columns"
	kanban_tasks "github.com/coorpe-app/coorpe/libs/bus/tasks"
	"github.com/coorpe-app/coorpe/libs/config"
	"time"

	"github.com/nats-io/nats.go"
)

type Bus struct {
	ChatMessages Queue[channel_messages.ChatMessages, struct{}]
	Kanban       *kanbanBus
}

func NewNatsBus(nc *nats.Conn) *Bus {
	return &Bus{
		ChatMessages: NewNatsQueue[channel_messages.ChatMessages, struct{}](
			nc,
			channel_messages.CHAT_MESSAGES_SUBJECT,
			1*time.Minute,
			nats.GOB_ENCODER,
		),
		Kanban: &kanbanBus{
			UpdateBoard: NewNatsQueue[kanban_boards.Board, struct{}](
				nc,
				kanban_boards.KANBAN_BOARDS_SUBJECT,
				1*time.Minute,
				nats.GOB_ENCODER,
			),
			UpdateColumn: NewNatsQueue[kanban_columns.Column, struct{}](
				nc,
				kanban_columns.KANBAN_COLUMNS_SUBJECT,
				1*time.Minute,
				nats.GOB_ENCODER,
			),
			UpdateTask: NewNatsQueue[kanban_tasks.Task, struct{}](
				nc,
				kanban_tasks.KANBAN_TASKS_SUBJECT,
				1*time.Minute,
				nats.GOB_ENCODER,
			),
		},
	}
}

func NewNatsBusFx(serviceName string) func(config config.Config) (*Bus, error) {
	return func(config config.Config) (*Bus, error) {
		nc, err := nats.Connect(config.NatsURL, nats.Name(serviceName))
		if err != nil {
			return nil, err
		}

		return NewNatsBus(nc), nil
	}
}
