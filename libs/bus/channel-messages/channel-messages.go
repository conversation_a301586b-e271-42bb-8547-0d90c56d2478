package channel_messages

import (
	"github.com/google/uuid"
	"time"
)

type ChatMessages struct {
	ID             uuid.UUID `json:"id"`
	Body           string    `json:"body"`
	WorkspaceID    string    `json:"workspaceId"`
	Type           string    `json:"type"`
	ChannelID      *string   `json:"channelId"`
	ConversationID *string   `json:"conversationId"`
	SenderID       uuid.UUID `json:"senderId"`
	SenderName     string    `json:"senderName"`
	SenderAvatar   string    `json:"senderAvatar"`
	CreatedAt      time.Time `json:"createdAt"`
	UpdatedAt      time.Time `json:"updatedAt"`
}

const (
	CHAT_MESSAGES_SUBJECT = "chat.messages"
)
