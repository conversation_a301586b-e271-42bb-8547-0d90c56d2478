package workspace_members

import (
	"github.com/coorpe-app/coorpe/libs/repositories/workspace_members/model"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type gormRepository struct {
	db *gorm.DB
}

func New(db *gorm.DB) Repository {
	return &gormRepository{db}
}

func (g *gormRepository) GetRole(id string, userID uuid.UUID) (model.WorkspaceMemberRole, error) {
	var member model.WorkspaceMemberRole
	err := g.db.Table("workspace_members").
		Where("workspace_id = ? AND user_id = ?", id, userID).
		Select("role").
		First(&member).Error

	if err != nil {
		return model.WorkspaceMemberRoleNil, err
	}

	return member, nil
}

func (g *gormRepository) GetUserIDByEmail(id string, email string) (string, error) {
	var userID string
	err := g.db.Table("workspace_members").
		Joins("JOIN users u ON workspace_members.user_id = u.id").
		Where("workspace_id = ? AND u.email = ?", id, email).
		Select("u.id").
		First(&userID).Error

	if err != nil {
		return "", err
	}

	return userID, nil
}

func (g *gormRepository) GetMany(workspaceID string) ([]model.WorkspaceMember, error) {
	var users []model.WorkspaceMember
	err := g.db.Table("workspace_members wm").
		Select("wm.id, wm.user_id, users.name, users.avatar_url, users.email, wm.role, wm.last_active, wm.invited_by").
		Joins("JOIN users ON wm.user_id = users.id").
		Where("wm.workspace_id = ?", workspaceID).
		Find(&users).Error

	if err != nil {
		return nil, err
	}

	return users, err
}
