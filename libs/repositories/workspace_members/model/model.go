package model

import (
	"github.com/google/uuid"
	"time"
)

type WorkspaceMember struct {
	WorkspaceID   string
	UserID        uuid.UUID
	UserName      string
	UserAvatarURL string
	UserEmail     string
	Role          WorkspaceMemberRole
	LastActive    time.Time
	InvitedBy     uuid.UUID
}

type WorkspaceMemberRole string

var WorkspaceMemberNil = WorkspaceMember{}
var WorkspaceMemberRoleNil = WorkspaceMemberRole("")
