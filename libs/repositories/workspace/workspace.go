package workspace

import (
	"github.com/coorpe-app/coorpe/libs/repositories/workspace/model"
	"github.com/google/uuid"
	"github.com/guregu/null"
	"gorm.io/gorm"
)

type Repository interface {
	Create(db *gorm.DB, input CreateInput) (model.Workspace, error)
	Update(workspace model.Workspace) error
	GetMany(input GetManyInput) (GetManyOutput, error)
	GetByID(id string) (model.Workspace, error)
	GetByChannelID(channelID string) (model.Workspace, error)
}

type CreateInput struct {
	ID          string
	Name        string
	Description null.String
	IconURL     null.String
}

type GetManyInput struct {
	UserID uuid.UUID
	Page   int
	Limit  int
}

type GetManyOutput struct {
	Items []model.Workspace
	Total int64
}
