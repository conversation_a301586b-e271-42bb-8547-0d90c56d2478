package boards

import (
	"github.com/coorpe-app/coorpe/libs/repositories/boards/model"
	"gorm.io/gorm"
)

type gormRepository struct {
	db *gorm.DB
}

func New(db *gorm.DB) Repository {
	return &gormRepository{
		db: db,
	}
}

func (g gormRepository) GetMany(workspaceID string) ([]model.Board, error) {
	var boards []model.Board
	err := g.db.Where("workspace_id = ?", workspaceID).Find(&boards).Error
	if err != nil {
		return nil, err
	}
	return boards, nil
}

func (g gormRepository) GetByID(id string) (model.Board, error) {
	var board model.Board
	err := g.db.Where("id = ?", id).First(&board).Error
	if err != nil {
		return model.Nil, err
	}
	return board, nil
}

func (g gormRepository) Create(board model.Board) (model.Board, error) {
	err := g.db.Create(&board).Error
	if err != nil {
		return model.Nil, err
	}
	return board, nil
}

func (g gormRepository) Update(board model.Board) (model.Board, error) {
	err := g.db.Save(&board).Error
	if err != nil {
		return model.Nil, err
	}
	return board, nil
}

func (g gormRepository) Delete(id string) error {
	err := g.db.Where("id = ?", id).Delete(&model.Board{}).Error
	if err != nil {
		return err
	}
	return nil
}

func (g gormRepository) GetByWorkspaceID(workspaceID string) ([]model.Board, error) {
	var boards []model.Board
	err := g.db.Where("workspace_id = ?", workspaceID).Find(&boards).Error
	if err != nil {
		return nil, err
	}
	return boards, nil
}
