package channel

import (
	"github.com/coorpe-app/coorpe/libs/repositories/channel/model"
	"gorm.io/gorm"
)

type GormRepository struct {
	db *gorm.DB
}

func New(
	db *gorm.DB,
) Repository {
	return &GormRepository{
		db,
	}
}

func (r *GormRepository) GetMany(input GetManyInput) (GetManyOutput, error) {
	var channels []model.Channel

	offset := (input.Page - 1) * input.Limit

	query := r.db.Table("channels").
		Select(
			`channels.id, channels.name, channels.description, channels.is_private, channels.workspace_id,
			(SELECT COUNT(*) FROM channel_members WHERE channel_members.channel_id = channels.id) AS members_count`,
		).
		Where("workspace_id = ?", input.WorkspaceID).
		Where(
			"is_private = false OR id IN (SELECT channel_id FROM channel_members WHERE user_id = ?)",
			input.UserID,
		).
		Order("created_at DESC").
		Limit(input.Limit).
		Offset(offset)

	if err := query.Scan(&channels).Error; err != nil {
		return GetManyOutput{}, err
	}

	if channels == nil {
		channels = []model.Channel{}
	}

	return GetManyOutput{
		Items: channels,
		Total: query.RowsAffected,
	}, nil
}

func (r *GormRepository) Create(db *gorm.DB, input *CreateInput) (model.Channel, error) {
	var result model.Channel

	query := `
		INSERT INTO channels (id, name, description, is_private, workspace_id)
		VALUES (?, ?, ?, ?, ?)
		RETURNING id, name, description, is_private, workspace_id
	`
	if err := db.Raw(
		query,
		input.ID,
		input.Name,
		input.Description,
		input.IsPrivate,
		input.WorkspaceID,
	).Scan(&result).Error; err != nil {
		return model.Nil, err
	}

	return result, nil
}

func (r *GormRepository) CountByID(id string) (int64, error) {
	var count int64
	err := r.db.Model(&model.Channel{}).
		Where("id = ?", id).
		Count(&count).Error
	return count, err
}
