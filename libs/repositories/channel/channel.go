package channel

import (
	"github.com/coorpe-app/coorpe/libs/repositories/channel/model"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	GetMany(input GetManyInput) (GetManyOutput, error)
	Create(db *gorm.DB, data *CreateInput) (model.Channel, error)
	CountByID(channelID string) (int64, error)
}

type CreateInput struct {
	ID          string
	Name        string
	Description *string
	IsPrivate   bool
	WorkspaceID string
}

type GetManyInput struct {
	Page        int
	Limit       int
	WorkspaceID string
	UserID      uuid.UUID
}

type GetManyOutput struct {
	Items []model.Channel
	Total int64
}
