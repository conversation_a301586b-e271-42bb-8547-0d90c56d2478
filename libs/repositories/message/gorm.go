package message

import (
	"github.com/coorpe-app/coorpe/libs/repositories/message/model"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"time"
)

type GormRepository struct {
	db *gorm.DB
}

func New(db *gorm.DB) Repository {
	return &GormRepository{
		db,
	}
}

func (r *GormRepository) FindByID(id uuid.UUID) (model.Message, error) {
	var message model.Message
	err := r.db.Where("id = ?", id).First(&message).Error
	return message, err
}

func (r *GormRepository) GetMany(input GetManyInput) (GetManyOutput, error) {
	var messages []model.Message

	offset := (input.Page - 1) * input.Limit

	query := r.db.Table("messages m").
		Select("m.id, m.body, m.channel_id, m.workspace_id, m.type, m.conversation_id, m.sender_id, users.name AS sender_name, users.avatar_url AS sender_avatar, m.created_at, m.updated_at").
		Where("channel_id = ? AND m.deleted_at IS NULL", input.ChannelID).
		Joins("JOIN users ON users.id = m.sender_id").
		Limit(input.Limit).
		Offset(offset)

	if err := query.Scan(&messages).Error; err != nil {
		return GetManyOutput{}, err
	}

	return GetManyOutput{
		Items: messages,
		Total: query.RowsAffected,
	}, nil
}

func (r *GormRepository) Create(input CreateInput) (model.Message, error) {
	msgInput := map[string]interface{}{
		"id":              uuid.New(),
		"body":            input.Body,
		"workspace_id":    input.WorkspaceID,
		"channel_id":      input.ChannelID,
		"conversation_id": input.ConversationID,
		"sender_id":       input.SenderID,
		"type":            input.Type,
		"created_at":      time.Now(),
		"updated_at":      time.Now(),
	}

	if err := r.db.Table("messages").Create(msgInput).Error; err != nil {
		return model.Nil, err
	}

	var result model.Message
	query := `
        SELECT m.id, m.body, m.workspace_id, m.channel_id, m.conversation_id, m.type, m.sender_id, 
               u.name AS sender_name, u.avatar_url AS sender_avatar,
               m.created_at, m.updated_at
        FROM messages m
        LEFT JOIN users u ON u.id = m.sender_id
        WHERE m.id = ?
    `

	if err := r.db.Raw(query, msgInput["id"]).Scan(&result).Error; err != nil {
		return model.Nil, err
	}

	return result, nil
}
