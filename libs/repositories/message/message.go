package message

import (
	"github.com/coorpe-app/coorpe/libs/repositories/message/model"
	"github.com/google/uuid"
)

type Repository interface {
	GetMany(input GetManyInput) (GetManyOutput, error)
	Create(message CreateInput) (model.Message, error)
	FindByID(id uuid.UUID) (model.Message, error)
}

type GetManyInput struct {
	ChannelID string
	Page      int
	Limit     int
}

type GetManyOutput struct {
	Items []model.Message
	Total int64
}

type CreateInput struct {
	Body           string
	WorkspaceID    string
	ChannelID      *string
	ConversationID *string
	Type           string
	SenderID       uuid.UUID
}
