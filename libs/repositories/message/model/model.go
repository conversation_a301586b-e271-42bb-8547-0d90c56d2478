package model

import (
	"github.com/google/uuid"
	"time"
)

type Message struct {
	ID             uuid.UUID `json:"id"`
	Body           string    `json:"body"`
	WorkspaceID    string    `json:"workspaceId"`
	ChannelID      *string   `json:"channelId"`
	ConversationID *string   `json:"conversationId"`
	SenderID       uuid.UUID `json:"senderId"`
	SenderName     string    `json:"senderName"`
	SenderAvatar   string    `json:"senderAvatar"`
	Type           string    `json:"type"`
	CreatedAt      time.Time `json:"createdAt"`
	UpdatedAt      time.Time `json:"updatedAt"`
}

var Nil = Message{}
