package user

import (
	"github.com/coorpe-app/coorpe/libs/repositories/user/model"
	"gorm.io/gorm"
)

type GormRepository struct {
	db *gorm.DB
}

func New(db *gorm.DB) Repository {
	return &GormRepository{
		db: db,
	}
}

func (r *GormRepository) ExistsByEmail(email string) (bool, error) {
	var exists bool
	err := r.db.Table("users").Where("email = ?", email).First(&exists).Error

	if err != nil {
		return false, err
	}

	return exists, nil
}

func (r *GormRepository) GetByEmail(email string) (model.User, error) {
	var user model.User
	err := r.db.Where("email = ?", email).First(&user).Error

	if err != nil {
		return model.Nil, err
	}

	return user, nil
}

func (r *GormRepository) Create(input CreateInput) (model.User, error) {
	var result model.User

	query := `
		INSERT INTO users (id, name, birthday, email, email_verified, avatar_url, password)
		VALUES (?, ?, ?, ?, ?, ?, ?)
		RETURNING id, name, birthday, email, email_verified, avatar_url, created_at
	`
	if err := r.db.Raw(
		query,
		input.ID,
		input.Name,
		input.Birthday,
		input.Email,
		input.EmailVerified,
		input.AvatarURL,
		input.Password,
	).Scan(&result).Error; err != nil {
		return model.Nil, err
	}

	return result, nil
}
