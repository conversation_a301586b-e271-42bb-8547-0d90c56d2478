package model

import (
	"github.com/google/uuid"
	"time"
)

type User struct {
	ID uuid.UUID `json:"id"`

	Name          string    `json:"name"`
	Birthday      time.Time `json:"birthday"`
	Email         string    `json:"email"`
	EmailVerified bool      `json:"emailVerified"`
	AvatarURL     string    `json:"avatarURL"`
	Password      string    `json:"password"`

	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

var Nil = User{}
