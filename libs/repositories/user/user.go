package user

import (
	"github.com/coorpe-app/coorpe/libs/repositories/user/model"
	"github.com/google/uuid"
	"time"
)

type Repository interface {
	ExistsByEmail(email string) (bool, error)
	GetByEmail(email string) (model.User, error)
	Create(input CreateInput) (model.User, error)
}

type CreateInput struct {
	ID            uuid.UUID
	Name          string
	Birthday      time.Time
	Email         string
	EmailVerified bool
	AvatarURL     string
	Password      string
}
