package model

import (
	"github.com/google/uuid"
	"time"
)

type Task struct {
	ID          string
	Title       string
	Description *string
	Status      TaskStatusEnum
	ColumnID    string
	AssigneeID  *uuid.UUID
	Position    int32
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

type TaskStatusEnum string

func (c TaskStatusEnum) String() string {
	return string(c)
}

const (
	TaskStatusTypeOpen       TaskStatusEnum = "OPEN"
	TaskStatusTypeInProgress TaskStatusEnum = "IN_PROGRESS"
	TaskStatusTypeCompleted  TaskStatusEnum = "COMPLETED"
	TaskStatusTypeCancelled  TaskStatusEnum = "CANCELLED"
)

var Nil = Task{}
