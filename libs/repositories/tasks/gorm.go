package tasks

import (
	"github.com/coorpe-app/coorpe/libs/repositories/tasks/model"
	"gorm.io/gorm"
)

type gormRepository struct {
	db *gorm.DB
}

func New(db *gorm.DB) Repository {
	return &gormRepository{
		db: db,
	}
}

func (g gormRepository) GetMany(columnID string) ([]model.Task, error) {
	var tasks []model.Task
	err := g.db.Where("column_id = ?", columnID).Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (g gormRepository) GetByID(id string) (model.Task, error) {
	var task model.Task
	err := g.db.Where("id = ?", id).First(&task).Error
	if err != nil {
		return model.Nil, err
	}
	return task, nil
}

func (g gormRepository) Create(task CreateInput) (model.Task, error) {
	err := g.db.Create(&task).Error
	if err != nil {
		return model.Nil, err
	}
	return model.Task{
		ID:          task.ID,
		Title:       task.Title,
		Description: task.Description,
		Status:      task.Status,
		ColumnID:    task.ColumnID,
		AssigneeID:  task.AssigneeID,
		Position:    task.Position,
	}, nil
}

func (g gormRepository) Update(task UpdateInput) (model.Task, error) {
	err := g.db.Save(&task).Error
	if err != nil {
		return model.Nil, err
	}

	return model.Task{
		Title:       task.Title,
		Description: task.Description,
		Status:      task.Status,
		ColumnID:    task.ColumnID,
		AssigneeID:  task.AssigneeID,
		Position:    task.Position,
	}, nil
}

func (g gormRepository) Delete(id string) error {
	err := g.db.Where("id = ?", id).Delete(&model.Task{}).Error
	if err != nil {
		return err
	}
	return nil
}

func (g gormRepository) GetByColumnID(columnID string) ([]model.Task, error) {
	var tasks []model.Task
	err := g.db.Where("column_id = ?", columnID).Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (g gormRepository) GetByBoardID(boardID string) ([]model.Task, error) {
	var tasks []model.Task
	err := g.db.Where("board_id = ?", boardID).Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (g gormRepository) GetByChannelID(channelID string) ([]model.Task, error) {
	var tasks []model.Task
	err := g.db.Where("channel_id = ?", channelID).Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}
