package tasks

import (
	"github.com/coorpe-app/coorpe/libs/repositories/tasks/model"
	"github.com/google/uuid"
)

type Repository interface {
	GetMany(columnID string) ([]model.Task, error)
	GetByID(id string) (model.Task, error)
	Create(task CreateInput) (model.Task, error)
	Update(task UpdateInput) (model.Task, error)
	Delete(id string) error
	GetByColumnID(columnID string) ([]model.Task, error)
	GetByBoardID(boardID string) ([]model.Task, error)
	GetByChannelID(channelID string) ([]model.Task, error)
}

type CreateInput struct {
	ID          string
	Title       string
	Description *string
	Status      model.TaskStatusEnum
	ColumnID    string
	AssigneeID  *uuid.UUID
	Position    int32
}

type UpdateInput struct {
	Title       string
	Description *string
	Status      model.TaskStatusEnum
	ColumnID    string
	AssigneeID  *uuid.UUID
	Position    int32
}
