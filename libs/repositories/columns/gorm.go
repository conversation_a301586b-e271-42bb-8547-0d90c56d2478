package columns

import (
	"github.com/coorpe-app/coorpe/libs/repositories/columns/model"
	"gorm.io/gorm"
)

type gormRepository struct {
	db *gorm.DB
}

func New(db *gorm.DB) Repository {
	return &gormRepository{
		db: db,
	}
}

func (g gormRepository) GetMany(boardID string) ([]model.Column, error) {
	var columns []model.Column
	err := g.db.Where("board_id = ?", boardID).Find(&columns).Error
	if err != nil {
		return nil, err
	}
	return columns, nil
}

func (g gormRepository) GetByID(id string) (model.Column, error) {
	var column model.Column
	err := g.db.Where("id = ?", id).First(&column).Error
	if err != nil {
		return model.Nil, err
	}
	return column, nil
}

func (g gormRepository) Create(column model.Column) (model.Column, error) {
	err := g.db.Create(&column).Error
	if err != nil {
		return model.Nil, err
	}
	return column, nil
}

func (g gormRepository) GetByBoardID(boardID string) ([]model.Column, error) {
	var columns []model.Column
	err := g.db.Where("board_id = ?", boardID).Find(&columns).Error
	if err != nil {
		return nil, err
	}
	return columns, nil
}

func (g gormRepository) Update(column model.Column) (model.Column, error) {
	err := g.db.Save(&column).Error
	if err != nil {
		return model.Nil, err
	}
	return column, nil
}

func (g gormRepository) Delete(id string) error {
	err := g.db.Where("id = ?", id).Delete(&model.Column{}).Error
	if err != nil {
		return err
	}
	return nil
}
