package channel_members

import (
	"github.com/coorpe-app/coorpe/libs/repositories/channel_members/model"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type GormRepository struct {
	db *gorm.DB
}

func New(db *gorm.DB) Repository {
	return &GormRepository{db}
}

func (r *GormRepository) GetMany(channelID string) ([]model.ChannelMember, error) {
	var members []model.ChannelMember
	err := r.db.Table("channel_members").
		Select(`users.id, users.name, users.birthday, users.email, users.avatar_url, channel_members.is_muted, channel_members.last_active`).
		Joins("JOIN users ON users.id = channel_members.user_id").
		Where("channel_members.channel_id = ?", channelID).
		Scan(&members).Error
	return members, err
}

func (r *GormRepository) Create(input CreateInput) (model.ChannelMember, error) {
	var result model.ChannelMember

	query := `
		INSERT INTO channel_members (channel_id, user_id)
		VALUES (?, ?)
		RETURNING channel_id, user_id, is_muted, last_active
	`

	if err := r.db.Raw(query, input.ChannelID, input.UserID).Scan(&result).Error; err != nil {
		return model.Nil, err
	}

	return result, nil
}

func (r *GormRepository) Delete(channelID string, userID uuid.UUID) error {
	return r.db.Where("channel_id = ? AND user_id = ?", channelID, userID).
		Delete(&model.ChannelMember{}).Error
}

func (r *GormRepository) IsMember(channelID string, userID uuid.UUID) (bool, error) {
	var count int64
	err := r.db.Model(&model.ChannelMember{}).
		Where("channel_id = ? AND user_id = ?", channelID, userID).
		Count(&count).Error
	return count > 0, err
}
