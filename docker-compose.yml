services:
  postgres:
    image: postgres:17
    restart: always
    environment:
      POSTGRES_USER: coorpe
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-coorpe}
      POSTGRES_DB: coorpe
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U coorpe"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:latest
    restart: always
    volumes:
      - redis_data:/data

  nats:
    image: nats:latest
    restart: always
    command: "--jetstream"

  minio:
    image: minio/minio:latest
    restart: always
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minio}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minio}
    volumes:
      - minio_data:/data
    command: server /data

  api:
    build:
      context: .
      dockerfile: apps/API/Coorpe.API/Dockerfile
    restart: always
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
      nats:
        condition: service_started
      minio:
        condition: service_started
    environment:
      APP_ENV: production
      POSTGRES_URL: postgres://coorpe:${POSTGRES_PASSWORD:-coorpe}@postgres:5432/coorpe
      NATS_URL: nats://nats:4222
      REDIS_URL: redis://redis:6379
      API_PORT: 8080
      S3_HOST: ${S3_HOST}
      S3_ACCESS_TOKEN: ${S3_ACCESS_TOKEN}
      S3_SECRET_TOKEN: ${S3_SECRET_TOKEN}
      S3_BUCKET: ${S3_BUCKET}
      S3_REGION: ${S3_REGION}
      S3_PUBLIC_URL: ${S3_PUBLIC_URL}
      GMAIL_MAIL: ${GMAIL_MAIL}
      GMAIL_PASSWORD: ${GMAIL_PASSWORD}
      GMAIL_SERVER_HOST: ${GMAIL_SERVER_HOST}
      GMAIL_SERVER_PORT: ${GMAIL_SERVER_PORT}

  frontend:
    build:
      context: apps/frontend
      dockerfile: Dockerfile
    restart: always
    depends_on:
      - api
    environment:
      VITE_API_URL: ${VITE_API_URL:-httpserver://localhost:8080}

  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf:/etc/nginx/conf.d
      - ./nginx/certbot/conf:/etc/letsencrypt
      - ./nginx/certbot/www:/var/www/certbot
    depends_on:
      - api
      - frontend

volumes:
  postgres_data:
  redis_data:
  minio_data: