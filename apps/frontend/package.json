{"name": "@coorpe/frontend", "private": true, "type": "module", "scripts": {"dev": "pnpx vite", "build": "bun run codegen && vue-tsc && pnpx vite build", "preview": "pnpx vite preview", "analyze": "pnpx vite-bundle-visualizer", "generate-pwa-assets": "pnpx pwa-assets-generator", "shadcn": "pnpx shadcn-vue"}, "dependencies": {"@tanstack/vue-query": "^5.73.0", "@tanstack/vue-virtual": "^3.13.8", "@tiptap/extension-placeholder": "^2.11.9", "@tiptap/pm": "^2.11.9", "@tiptap/starter-kit": "^2.11.9", "@tiptap/vue-3": "^2.11.9", "@vee-validate/zod": "^4.15.0", "@vueuse/components": "^13.1.0", "@vueuse/core": "^13.1.0", "@vueuse/router": "^13.1.0", "@zero-dependency/utils": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-vue-next": "^0.487.0", "reka-ui": "^2.2.0", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "zod": "^3.24.2"}, "devDependencies": {"@plugin-web-update-notification/vite": "^2.0.0", "@spiriit/vite-plugin-svg-spritemap": "^4.0.1", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.3", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "tailwindcss": "^4.1.3", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}, "packageManager": "pnpm@10.7.1+sha512.2d92c86b7928dc8284f53494fb4201f983da65f0fb4f0d40baafa5cf628fa31dae3e5968f12466f17df7e97310e30f343a648baea1b9b350685dafafffdf5808"}