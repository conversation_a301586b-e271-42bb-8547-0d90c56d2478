import {fileURLToPath, URL} from "node:url";

import {webUpdateNotice} from "@plugin-web-update-notification/vite";
import tailwindcss from "@tailwindcss/vite";
import vue from "@vitejs/plugin-vue";
import {defineConfig, loadEnv} from "vite";
import vueDevTools from "vite-plugin-vue-devtools";
import VitePluginSvgSpritemap from "@spiriit/vite-plugin-svg-spritemap";

// https://vite.dev/config/
export default defineConfig(({mode}) => {
  const env = loadEnv(mode, process.cwd(), "");

  return {
    plugins: [
      vue(),
      tailwindcss(),
      vueDevTools(),
      webUpdateNotice({
        notificationProps: {
          title: "Nova versão",
          description:
            "Uma atualização disponível, por favor atualize a página para obter os últimos recursos e correções de bugs!",
          buttonText: "atualizar",
          dismissButtonText: "cancelar",
        },
        checkInterval: 60 * 1000,
      }),
      VitePluginSvgSpritemap(["./src/assets/*/*.svg", "./src/assets/*.svg"]),
    ],
    resolve: {
      alias: {
        vue: "vue/dist/vue.esm-bundler.js",
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },
    server: {
      port: 3000,
      host: true,
      proxy: {
        "/api": {
          target: env.VITE_API_URL,
          changeOrigin: true,
        },
      },
      hmr: {
        protocol: env.USE_WSS === "true" ? "wss" : "ws",
      },
    },
    clearScreen: false,
  };
});
