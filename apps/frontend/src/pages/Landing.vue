<script lang="ts" setup>
import Footer from "@/features/landing/ui/footer.vue";
import Header from "@/features/landing/ui/header.vue";
import Hero from "@/features/landing/ui/hero.vue";
import Solutions from "@/features/landing/ui/solutions.vue";
import {But<PERSON>} from "@/components/ui/button";
</script>

<template>
  <Header/>
  <Hero
    announcement="Nova versão disponível"
    className="bg-gradient-to-b from-primary/20 to-background pt-20"
    imageSrc="https://cdn.dribbble.com/userupload/7646117/file/original-32dd39d18eb55df7b9971bc78bccdca5.png"
    subtitle="Uma plataforma completa para gerenciar seus projetos, equipes e tarefas de forma simples e eficiente. Integre suas ferramentas favoritas e aumente a produtividade."
    title="Simplifique o gerenciamento de projetos e equipes"
  >
    <div class="flex flex-wrap gap-4 mt-6">
      <Button class="gap-2" size="lg">Começar agora</Button>
      <Button size="lg" variant="outline">Agendar demonstração</Button>
    </div>
  </Hero>

  <Solutions/>

  <Footer
    description="Uma plataforma completa para gerenciar seus projetos, equipes e tarefas de forma simples e eficiente."
    title="Coorpe"
  />
</template>
