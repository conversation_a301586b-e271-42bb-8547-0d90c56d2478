<script lang="ts" setup>
import PageLayout, {PageLayoutTab} from "@/layout/page-layout.vue";
import {computed, ref} from "vue";
import {useChannels} from "@/features/channels/composables/use-channels.ts";
import ChannelMembersSelector from "@/features/channels-members/ui/channel-members-selector.vue";
import ChannelMessages from "@/features/channel-messages/channel-messages.vue";

const {currentChannel} = useChannels()

const tabs = computed<PageLayoutTab[]>(() => ([
  {name: "canal", title: "Canal", component: ChannelMessages},
  {name: "media", title: "Media", component: null},
]))

const activeTab = ref("canal");

</script>

<template>
  <PageLayout
    :activeTab="activeTab"
    :tabs="tabs"
    clean-body
  >
    <template #title>
      {{ currentChannel?.name }}
    </template>
    <template #description>
      {{ currentChannel?.description }}
    </template>
    <template #action>
      <ChannelMembersSelector/>
    </template>
  </PageLayout>
</template>
