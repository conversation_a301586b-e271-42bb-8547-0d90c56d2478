<script lang="ts" setup>
import {ref, watch} from "vue";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {ScrollArea} from "@/components/ui/scroll-area";

const props = defineProps({
  startYear: {
    type: Number,
    required: true,
  },
  endYear: {
    type: Number,
    required: true,
  },
  value: String,
  onChange: Function,
  onBlur: Function,
});

const selectedDay = ref("");
const selectedMonth = ref("");
const selectedYear = ref("");

const months = [
  "Janeiro",
  "Fevereiro",
  "Março",
  "Abril",
  "Maio",
  "Junho",
  "Julho",
  "Agosto",
  "Setembro",
  "Outubro",
  "Novembro",
  "Dezembro",
];

watch([selectedDay, selectedMonth, selectedYear], () => {
  if (selectedDay.value && selectedMonth.value && selectedYear.value) {
    const day = parseInt(selectedDay.value);
    const month = months.indexOf(selectedMonth.value);
    const year = parseInt(selectedYear.value);
    const date = new Date(year, month, day);

    const isoDate = date.toISOString();
    props.onChange?.(isoDate);
  }
});

const handleChange = (type: "day" | "month" | "year", value: any) => {
  switch (type) {
    case "day":
      selectedDay.value = value;
      break;
    case "month":
      selectedMonth.value = value;
      break;
    case "year":
      selectedYear.value = value;
      break;
  }
};
</script>
<template>
  <div class="grid grid-cols-3 gap-4 max-w-[360px] dark:text-white">
    <Select class="w-full" @update:modelValue="(v) => handleChange('day', v)">
      <SelectTrigger class="w-full">
        <SelectValue placeholder="Dia"/>
      </SelectTrigger>
      <SelectContent>
        <ScrollArea class="h-48">
          <SelectItem v-for="i in 31" :key="i" :value="i.toString()">
            {{ i }}
          </SelectItem>
        </ScrollArea>
      </SelectContent>
    </Select>
    <Select class="w-full" @update:modelValue="(v) => handleChange('month', v)">
      <SelectTrigger class="w-full">
        <SelectValue placeholder="Mês"/>
      </SelectTrigger>
      <SelectContent>
        <ScrollArea class="h-48">
          <SelectItem v-for="month in months" :key="month" :value="month">
            {{ month }}
          </SelectItem>
        </ScrollArea>
      </SelectContent>
    </Select>
    <Select class="w-full" @update:modelValue="(v) => handleChange('year', v)">
      <SelectTrigger class="w-full">
        <SelectValue placeholder="Ano"/>
      </SelectTrigger>
      <SelectContent>
        <ScrollArea class="h-48">
          <SelectItem
            v-for="year in [...Array(props.endYear - props.startYear + 1)].map(
              (_, i) => props.endYear - i
            )"
            :key="year"
            :value="year.toString()"
          >
            {{ year }}
          </SelectItem>
        </ScrollArea>
      </SelectContent>
    </Select>
  </div>
</template>
