<script lang="ts" setup>
import { loginFormSchema, useAuthApi } from "@/api/auth.ts";
import Logo from "@/assets/logo.svg?use";
import { Button, buttonVariants } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Loader2 } from "lucide-vue-next";
import { useForm } from "vee-validate";

const form = useForm({
  validationSchema: loginFormSchema,
  validateOnMount: false,
  keepValuesOnUnmount: true,
});

const authManager = useAuthApi();
const { mutate, isPending, error } = authManager.useMutationLogin();

const onSubmit = form.handleSubmit((values) => {
  mutate(values);
});
</script>

<template>
  <Card class="mx-auto h-full sm:h-auto w-full sm:w-[400px] justify-center flex flex-col">
    <CardHeader class="flex flex-col items-center">
      <Logo class="size-16" />
      <CardTitle class="text-center">Registre-se para continuar</CardTitle>
    </CardHeader>
    <CardContent class="space-y-4">
      <form class="space-y-4" @submit="onSubmit">
        <FormField v-slot="{ componentField }" name="email">
          <FormItem>
            <FormLabel> Email</FormLabel>
            <FormControl>
              <Input placeholder="Digite seu email" type="email" v-bind="componentField" />
            </FormControl>
          </FormItem>
        </FormField>

        <FormField v-slot="{ componentField }" name="password">
          <FormItem>
            <FormLabel> Senha</FormLabel>
            <FormControl>
              <Input placeholder="Digite sua senha" type="password" v-bind="componentField" />
            </FormControl>
          </FormItem>
        </FormField>

        <p v-if="error" class="text-destructive text-sm mt-2">
          {{ error }}
        </p>

        <Button :disabled="isPending" class="w-full" type="submit">
          <Loader2 v-if="isPending" class="w-4 h-4 mr-2 animate-spin" />
          Continuar
        </Button>

        <div class="flex items-center gap-2">
          <router-link :class="buttonVariants({ variant: 'link' })" to="#">
            Nao consegue entrar?
          </router-link>
          •
          <router-link :class="buttonVariants({ variant: 'link' })" to="/register">
            Criar uma conta
          </router-link>
        </div>
      </form>
    </CardContent>
  </Card>
</template>
