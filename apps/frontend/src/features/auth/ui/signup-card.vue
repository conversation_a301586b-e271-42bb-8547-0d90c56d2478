<script lang="ts" setup>
import {registerFormSchema, useAuthApi,} from "@/api/auth.ts";
import Logo from "@/assets/logo.svg?use";
import {Button} from "@/components/ui/button";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {FormControl, FormField, FormItem, FormLabel} from "@/components/ui/form";
import {Input} from "@/components/ui/input";
import {PinInput, PinInputGroup, PinInputInput} from "@/components/ui/pin-input";
import {useCountdown} from "@vueuse/core";
import {Loader2} from "lucide-vue-next";
import {useForm} from "vee-validate";
import {computed, ref, shallowRef, watch} from "vue";
import BirthdayPicker from "./birthday-picker.vue";
import {useMailAuthenticationApi} from "@/api/mail.ts";
import {useToast} from '@/components/ui/toast/use-toast'

type RegisterStep = "register" | "verify" | "userData";

const step = ref<RegisterStep>("register");

const disabled = ref(true);
const code = ref<string[]>([]);
const email = ref("");

const registerForm = useForm({
  validationSchema: registerFormSchema,
  keepValuesOnUnmount: true,
});

const {toast} = useToast()

watch(email, () => {
  if (!email.value) return;

  registerForm.setFieldValue("email", email.value);
});

const authManager = useAuthApi()
const mailManager = useMailAuthenticationApi()

const mutationRegister = authManager.useMutationRegister();
const mutationSendCode = mailManager.useMutationSendCode();
const mutationVerifyCode = mailManager.useMutationVerifyCode();

const countdown = shallowRef(30);
const {remaining, start} = useCountdown(countdown);

async function handleSendMailCode() {
  try {
    await mutationSendCode.mutateAsync(email.value);

    step.value = "verify";
    start();
  } catch (error) {
    toast({
      title: "Falha ao enviar código de verificação",
      variant: "destructive",
    })
  }
}

async function handleVerifyMailCode() {
  try {
    await mutationVerifyCode.mutateAsync(code.value.join(""));

    step.value = "userData";
  } catch (error) {
    toast({
      title: "Falha ao verificar código de verificação",
      variant: "destructive",
    })
  }
}

const registerSubmit = registerForm.handleSubmit(async (values) => {
  await mutationRegister.mutateAsync(values);
});

const title = computed(() => {
  switch (step.value) {
    case "register":
      return "Registre-se para continuar";
    case "verify":
      return "Verifique seu e-mail";
    case "userData":
      return "Preencha seus dados";
  }
});
</script>

<template>
  <Card class="mx-auto h-full sm:h-auto w-full sm:w-[400px] justify-center flex flex-col">
    <CardHeader class="flex flex-col items-center">
      <Logo class="size-16"/>
      <CardTitle class="text-center">{{ title }}</CardTitle>
    </CardHeader>
    <CardContent class="space-y-4">
      <form
        v-if="step === 'register'"
        class="space-y-4 text-center"
        @submit.prevent="handleSendMailCode"
      >
        <Input v-model="email" :disabled="mutationSendCode.isPending" placeholder="Insira seu email"
               required
               type="email"/>

        <Button :disabled="mutationSendCode.isPending" class="w-full" type="submit">
          <Loader2 v-if="mutationSendCode.isPending" class="w-4 h-4 mr-2 animate-spin"/>
          Continuar
        </Button>

        <p class="text-sm text-muted-foreground">
          Ao clicar em "Continuar", você concorda com nossos Termos de Serviço e Política de
          Privacidade
        </p>

        <p>
          Já tem uma conta?
          <router-link class="underline" to="/login">Faça login</router-link>
        </p>
      </form>

      <div v-if="step === 'verify'" class="space-y-4 flex items-center flex-col">
        <p class="text-sm text-muted-foreground text-center">
          Digite o código enviado para o email {{ registerForm.values.email }}
        </p>

        <PinInput v-model="code" placeholder="○" @complete="disabled = false">
          <PinInputGroup>
            <PinInputInput v-for="(id, index) in 6" :key="id" :index="index"/>
          </PinInputGroup>
        </PinInput>

        <Button :disabled="!!remaining" variant="link" @click="handleSendMailCode">
          Reenviar e-mail <span v-if="!!remaining">({{ remaining }}s)</span>
        </Button>

        <Button
          :disabled="disabled"
          class="w-full"
          @click="handleVerifyMailCode"
        >
          <Loader2 v-if="mutationVerifyCode.isPending" class="w-4 h-4 mr-2 animate-spin"/>
          Continuar
        </Button>
      </div>

      <form v-if="step === 'userData'" class="space-y-4" @submit="registerSubmit">
        <FormField v-slot="{ componentField }" name="email">
          <FormItem>
            <FormLabel>Email</FormLabel>
            <FormControl>
              <Input disabled placeholder="Insira seu email" type="email" v-bind="componentField"/>
            </FormControl>
          </FormItem>
        </FormField>

        <FormField v-slot="{ componentField }" name="name">
          <FormItem>
            <FormLabel>Nome completo</FormLabel>
            <FormControl>
              <Input placeholder="Insira seu nome completo" type="text" v-bind="componentField"/>
            </FormControl>
          </FormItem>
        </FormField>

        <FormField v-slot="{ componentField }" name="password">
          <FormItem>
            <FormLabel>Senha</FormLabel>
            <FormControl>
              <Input placeholder="Insira sua senha" type="password" v-bind="componentField"/>
            </FormControl>
          </FormItem>
        </FormField>

        <FormField v-slot="{ componentField }" name="birthday">
          <FormItem>
            <FormLabel>Data de nascimento</FormLabel>
            <FormControl>
              <BirthdayPicker
                :endYear="new Date().getFullYear() - 3"
                :startYear="new Date().getFullYear() - 100"
                v-bind="componentField"
              />
            </FormControl>
          </FormItem>
        </FormField>

        <Button class="w-full" type="submit">
          <Loader2 v-if="mutationRegister.isPending" class="w-4 h-4 mr-2 animate-spin"/>
          Registre-se
        </Button>
      </form>
    </CardContent>
  </Card>
</template>
