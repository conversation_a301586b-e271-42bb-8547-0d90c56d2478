<script lang="ts" setup>
import {cn} from "@/lib/utils.ts";
import {ArrowRightIcon} from "lucide-vue-next";
import {onMounted, ref} from "vue";

interface HeroSectionProps {
  title: string;
  subtitle: string;
  imageSrc: string;
  announcement?: string;
  className?: string;
}

defineProps<HeroSectionProps>();

const heroRef = ref<HTMLDivElement | null>(null);
const inView = ref(false);

onMounted(() => {
  if (!heroRef.value) return;

  const observer = new IntersectionObserver(
    ([entry]) => {
      inView.value = entry.isIntersecting;
    },
    { rootMargin: "-100px" }
  );

  observer.observe(heroRef.value);

  return () => {
    if (heroRef.value) observer.unobserve(heroRef.value);
  };
});
</script>

<template>
  <section :class="cn('relative w-full py-16 bg-primary/20', className)">
    <div
      class="mx-auto max-w-[80rem] gap-8 flex md:flex-row flex-col items-center px-6 md:px-8 md:mt-[50px] mt-[20px]"
    >
      <div>
        <div
          v-if="announcement"
          class="backdrop-filter-[12px] animate-fade-in group inline-flex h-7 translate-y-[-1rem] items-center justify-between gap-1 rounded-full border border-primary/5 bg-primary/10 px-3 text-xs text-white transition-all ease-in hover:cursor-pointer hover:bg-primary/20"
        >
          <div class="inline-flex items-center justify-center">
            <span>✨ {{ announcement }}</span>
            <ArrowRightIcon
              class="ml-1 size-3 transition-transform duration-300 ease-in-out group-hover:translate-x-0.5"
            />
          </div>
        </div>

        <h1
          class="animate-fade-in translate-y-[-1rem] text-balance bg-primary bg-clip-text py-6 text-2xl font-bold leading-none tracking-tighter text-transparent [--animation-delay:200ms] dark:from-primary dark:to-primary/40 sm:text-3xl md:text-4xl lg:text-5xl"
        >
          {{ title }}
        </h1>
        <div class="flex items-center gap-2">
          <slot></slot>
        </div>
        <p
          class="animate-fade-in mt-8 text-balance text-lg tracking-tight [--animation-delay:400ms] md:text-xl"
        >
          {{ subtitle }}
        </p>
      </div>

      <div class="animate-fade-up relative [--animation-delay:400ms] [perspective:2000px]">
        <div class="rounded-3xl p-[12px] bg-white/30 ring-1 ring-white/40 aspect-video">
          <div
            ref="heroRef"
            :class="
              cn(
                'relative rounded-2xl bg-[color:rgb(var(--background)/0.01)]',
                'before:absolute before:bottom-1/2 before:left-0 before:top-0 before:h-full before:w-full before:opacity-0 before:[background-image:linear-gradient(to_bottom,var(--primary),var(--primary),transparent_80%)] before:[filter:blur(180px)]',
                inView && 'before:animate-image-glow'
              )
            "
          >
            <img
              :src="imageSrc"
              alt="Hero Image"
              class="relative h-full w-full rounded-[inherit] object-contain"
            />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
/* Add any required animations here */
@keyframes image-glow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.before\:animate-image-glow::before {
  animation: image-glow 1s ease-in-out forwards;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
  animation-delay: var(--animation-delay, 0ms);
  opacity: 0;
}

@keyframes fade-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-up {
  animation: fade-up 0.5s ease-out forwards;
  animation-delay: var(--animation-delay, 0ms);
  opacity: 0;
}
</style>
