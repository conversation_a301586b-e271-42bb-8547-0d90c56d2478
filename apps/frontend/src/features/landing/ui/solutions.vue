<script setup lang="ts">
import { Card, CardContent } from "@/components/ui/card";
import { Brain, Shield, Zap } from "lucide-vue-next";
import Section from "./section.vue";

const problems = [
  {
    title: "Integração Simplificada",
    description:
      "Conectamos suas ferramentas de gestão de projetos como Jira, Trello e outras, centralizando a comunicação e tornando o fluxo de trabalho mais eficiente.",
    icon: Brain,
  },
  {
    title: "Agilidade nos Processos",
    description:
      "Automatize a distribuição de tarefas e acompanhamento de projetos, eliminando gargalos e acelerando a entrega de resultados.",
    icon: Zap,
  },
  {
    title: "Gestão Transparente",
    description:
      "Mantenha toda a equipe alinhada com dashboards personalizados e relatórios em tempo real sobre o andamento dos projetos e atividades.",
    icon: Shield,
  },
];
</script>

<template>
  <Section title="Nossas soluções" subtitle="Integrações com as melhores ferramentas">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
      <div
        v-for="(problem, index) in problems"
        :key="index"
        class="transition-all duration-300 ease-in-out transform hover:scale-105"
      >
        <Card class="bg-background border-none shadow-none">
          <CardContent class="p-6 space-y-4">
            <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <component :is="problem.icon" class="w-6 h-6 text-primary" />
            </div>
            <h3 class="text-xl font-semibold">{{ problem.title }}</h3>
            <p class="text-muted-foreground">{{ problem.description }}</p>
          </CardContent>
        </Card>
      </div>
    </div>
  </Section>
</template>
