<script setup lang="ts">
import { Disc, LucideIcon, TwitterIcon } from "lucide-vue-next";
import Logo from "@/assets/logo.svg?use";

defineProps<{
  title: string;
  description: string;
}>();

interface FooterNavItem {
  href: string;
  name: string;
}

interface FooterNavSection {
  label: string;
  items: FooterNavItem[];
}

interface FooterSocial {
  href: string;
  name: string;
  icon: LucideIcon;
}

const footerNavs: FooterNavSection[] = [
  {
    label: "Produto",
    items: [
      { href: "https://app-business.solomon-ai.app", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>" },
      { href: "mailto:<EMAIL>", name: "<PERSON><PERSON>" },
    ],
  },
  {
    label: "Legal",
    items: [
      { href: "https://solomon-ai.app/terms", name: "Termos" },
      { href: "https://solomon-ai.app/policy", name: "Privacidade" },
    ],
  },
];

const footerSocials: FooterSocial[] = [
  {
    href: "",
    name: "Discord",
    icon: Disc,
  },
  {
    href: "",
    name: "Twitter",
    icon: TwitterIcon,
  },
];

const currentYear = new Date().getFullYear();
</script>

<template>
  <footer>
    <div class="mx-auto w-full max-w-screen-xl xl:pb-2 border-t">
      <div class="gap-4 p-4 px-8 py-16 sm:pb-16 md:flex md:justify-between">
        <div class="mb-12 flex flex-col gap-4">
          <router-link to="/" class="flex items-center gap-2">
            <Logo class="size-14" />
            <span class="self-center whitespace-nowrap text-2xl font-semibold dark:text-white">
              {{ title }}
            </span>
          </router-link>
          <p class="max-w-md">{{ description }}</p>
        </div>

        <div class="grid grid-cols-1 gap-8 sm:grid-cols-3 sm:gap-10">
          <div v-for="nav in footerNavs" :key="nav.label">
            <h2
              class="mb-6 text-sm font-medium uppercase tracking-tighter text-gray-900 dark:text-white"
            >
              {{ nav.label }}
            </h2>
            <ul class="grid gap-2">
              <li v-for="item in nav.items" :key="item.name">
                <a
                  :href="item.href"
                  class="cursor-pointer text-sm font-[450] text-gray-400 duration-200 hover:text-gray-200"
                >
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div
        class="flex flex-col gap-2 rounded-md border-neutral-700/20 px-8 py-4 sm:flex sm:flex-row sm:items-center sm:justify-between"
      >
        <div class="flex space-x-5 sm:mt-0 sm:justify-center">
          <a
            v-for="social in footerSocials"
            :key="social.name"
            :href="social.href"
            class="fill-gray-500 text-gray-500 hover:fill-gray-900 hover:text-gray-900 dark:hover:fill-gray-600 dark:hover:text-gray-600"
          >
            <component :is="social.icon" class="h-4 w-4" />
            <span class="sr-only">{{ social.name }}</span>
          </a>
        </div>

        <span class="text-sm text-gray-500 dark:text-gray-400 sm:text-center">
          Copyright © {{ currentYear }}
          <router-link to="/" class="cursor-pointer">
            {{ title }}
          </router-link>
          . All Rights Reserved.
        </span>
      </div>
    </div>
  </footer>
</template>
