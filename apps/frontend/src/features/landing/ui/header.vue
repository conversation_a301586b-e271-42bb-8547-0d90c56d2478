<script lang="ts" setup>
import { useProfile } from "@/api/auth.ts";
import Logo from "@/assets/logo.svg?use";
import { buttonVariants } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils.ts";
import { AlignJustify } from "lucide-vue-next";
import { ref } from "vue";

const { isAuthenticated } = useProfile();

const isOpen = ref(false);

const toggleMenu = () => {
  isOpen.value = !isOpen.value;
};

const closeMenu = () => {
  isOpen.value = false;
};

const menuItems = [
  { label: "Preços", href: "#" },
  { label: "Carreiras", href: "#" },
  { label: "Fale Conosco", href: "#" },
];
const funcionalidadesItems = [
  {
    label: "Canais",
    href: "/app",
    desc: "Converse em canais organizados por tópicos.",
  },
  {
    label: "Mensagens Diretas",
    href: "/app",
    desc: "Converse privadamente com colegas.",
  },
  {
    label: "Integrações",
    href: "/app",
    desc: "Conecte apps e serviços ao seu workspace.",
  },
];
</script>

<template>
  <header class="fixed left-0 top-0 z-50 w-full border-b bg-background">
    <div
      class="container flex h-[3.5rem] items-center justify-between max-w-screen-xl mx-auto px-4"
    >
      <div class="flex items-center space-x-4">
        <router-link :to="isAuthenticated ? '/app' : '/'" class="flex items-center">
          <Logo class="size-12" />
        </router-link>
        <NavigationMenu class="hidden md:flex">
          <NavigationMenuList>
            <NavigationMenuItem>
              <NavigationMenuTrigger>Funcionalidades</NavigationMenuTrigger>
              <NavigationMenuContent>
                <ul class="grid gap-3 p-4 w-56">
                  <li v-for="item in funcionalidadesItems" :key="item.href">
                    <router-link
                      :to="item.href"
                      class="block rounded-md px-2 py-1 hover:bg-muted transition-colors"
                    >
                      {{ item.label }}
                      <span class="block text-xs text-muted-foreground">{{ item.desc }}</span>
                    </router-link>
                  </li>
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
            <NavigationMenuItem v-for="item in menuItems" :key="item.href">
              <NavigationMenuLink as-child>
                <router-link
                  :to="item.href"
                  class="px-3 py-2 rounded-md text-sm font-medium hover:bg-muted transition-colors"
                >
                  {{ item.label }}
                </router-link>
              </NavigationMenuLink>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>
      </div>
      <div class="ml-auto hidden md:flex h-full items-center">
        <router-link
          :class="cn(buttonVariants(), 'ml-6 text-sm')"
          :to="isAuthenticated ? '/app' : '/login'"
        >
          {{ isAuthenticated ? "Criar um novo workspace" : "Entrar" }}
        </router-link>
      </div>

      <div class="md:hidden flex items-center">
        <button class="ml-2" @click="toggleMenu">
          <span class="sr-only">Toggle menu</span>
          <AlignJustify class="h-6 w-6" />
        </button>
      </div>
    </div>

    <transition name="fade">
      <div v-if="isOpen" class="fixed inset-0 z-50 bg-background flex flex-col">
        <div class="flex items-center justify-between px-4 pt-4">
          <router-link class="flex items-center" to="/" @click="closeMenu">
            <Logo class="size-10" />
          </router-link>
          <button class="p-2 text-3xl" @click="closeMenu">&times;</button>
        </div>
        <nav class="flex flex-col px-8 py-6 space-y-6">
          <details class="group">
            <summary
              class="text-2xl font-semibold cursor-pointer flex items-center justify-between"
            >
              Funcionalidades
              <span class="ml-2 text-lg">&#9662;</span>
            </summary>
            <ul class="pl-4 mt-2 flex flex-col space-y-2">
              <li v-for="item in funcionalidadesItems" :key="item.href">
                <router-link
                  :to="item.href"
                  class="block text-lg font-medium hover:text-primary transition-colors"
                  @click="closeMenu"
                >
                  {{ item.label }}
                  <span class="block text-xs text-muted-foreground">{{ item.desc }}</span>
                </router-link>
              </li>
            </ul>
          </details>
          <ul class="flex flex-col space-y-4">
            <li v-for="item in menuItems" :key="item.href">
              <router-link
                :to="item.href"
                class="text-2xl hover:text-primary transition-colors"
                @click="closeMenu"
              >
                {{ item.label }}
              </router-link>
            </li>
          </ul>
          <router-link
            :class="cn(buttonVariants({ variant: 'secondary' }), 'w-full mt-6')"
            :to="isAuthenticated ? '/app' : '/login'"
            @click="closeMenu"
          >
            {{ isAuthenticated ? "Criar um novo workspace" : "Entrar" }}
          </router-link>
        </nav>
      </div>
    </transition>
  </header>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
