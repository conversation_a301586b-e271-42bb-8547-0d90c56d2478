<script setup lang="ts">
interface SectionProps {
  id?: string;
  title?: string;
  subtitle?: string;
  description?: string;
  className?: string;
}

defineProps<SectionProps>();
</script>

<template>
  <section :id="id || (title ? title.toLowerCase().replace(/\s+/g, '-') : undefined)">
    <div :class="className">
      <div class="relative container mx-auto px-4 py-16 max-w-7xl">
        <div class="text-center space-y-4 pb-6 mx-auto">
          <h2
            v-if="title"
            class="text-sm text-primary font-mono font-medium tracking-wider uppercase"
          >
            {{ title }}
          </h2>
          <h3
            v-if="subtitle"
            class="mx-auto mt-4 max-w-xs text-3xl font-semibold sm:max-w-none sm:text-4xl md:text-5xl"
          >
            {{ subtitle }}
          </h3>
          <p v-if="description" class="mt-6 text-lg leading-8 text-slate-600 max-w-2xl mx-auto">
            {{ description }}
          </p>
        </div>
        <slot></slot>
      </div>
    </div>
  </section>
</template>
