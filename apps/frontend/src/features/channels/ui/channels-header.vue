<script lang="ts" setup>
import {useChannels} from "@/features/channels/composables/use-channels.ts";
import {HashIcon} from "lucide-vue-next";

const {currentChannel} = useChannels();
</script>
<template>
  <header class="flex items-center justify-between px-4 py-2 border-b border-border bg-background">
    <div class="flex items-center gap-2">
      <span class="text-xl font-bold gap-2 flex items-center">
        <HashIcon v-if="true"/>
        {{ currentChannel?.name || "Select a channel" }}
      </span>
      <span v-if="currentChannel?.description" class="text-muted-foreground text-sm ml-2">
        {{ currentChannel.description }}
      </span>
    </div>
    <div class="flex items-center gap-2">
    </div>
  </header>
</template>


