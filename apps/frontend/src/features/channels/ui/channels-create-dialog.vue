<script lang="ts" setup>
import {PlusIcon} from 'lucide-vue-next'
import {useForm} from 'vee-validate'
import {ref, watch} from 'vue'

import {But<PERSON>} from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {Switch} from "@/components/ui/switch";
import {FormControl, FormDescription, FormField, FormItem, FormLabel} from '@/components/ui/form'
import {Input} from '@/components/ui/input'
import {formSchema, useChannels} from "@/features/channels/composables/use-channels.ts";
import {toTypedSchema} from "@vee-validate/zod";
import {useRouteParams} from "@vueuse/router";

const open = ref(false)
const {createChannel} = useChannels()

const workspaceId = useRouteParams("workspaceId", String)

const form = useForm({
  validationSchema: toTypedSchema(formSchema),
  initialValues: {
    name: '',
    is_private: false,
    workspace_id: workspaceId.value,
  },
  validateOnMount: false,
})

watch(workspaceId, (newId) => {
  if (newId) {
    form.setFieldValue('workspace_id', newId)
  }
})

const handleSubmit = form.handleSubmit(async (values) => {
  try {
    const result = await createChannel(values)
    if (result) {
      form.resetForm()
      open.value = false
    }
  } catch (error) {
    console.error(error)
  }
})
</script>

<template>
  <Dialog v-model:open="open">
    <DialogTrigger as-child>
      <Button size="icon" variant="ghost">
        <PlusIcon class="size-4"/>
      </Button>
    </DialogTrigger>

    <DialogContent class="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>Criar novo canal</DialogTitle>
      </DialogHeader>

      <form class="space-y-4" @submit.prevent="handleSubmit">
        <FormField v-slot="{ componentField }" name="name">
          <FormItem>
            <FormLabel>Nome</FormLabel>
            <FormControl>
              <Input
                placeholder="Digite o nome do canal (e.g. 'novo-canal')"
                v-bind="componentField"
              />
            </FormControl>
          </FormItem>
        </FormField>

        <FormField
          v-slot="{ value, handleChange }"
          name="is_private"
        >
          <FormItem class="flex justify-between">
            <FormLabel>Canal privado</FormLabel>
            <FormControl>
              <Switch
                :model-value="value"
                @update:model-value="handleChange"
              />
            </FormControl>
          </FormItem>
          <FormDescription class="text-xs -mt-4">
            Canais privados são visíveis apenas para membros do workspace.
          </FormDescription>
        </FormField>

        <DialogFooter>
          <Button
            type="button"
            variant="link"
            @click="open = false"
          >
            Cancelar
          </Button>
          <Button type="submit">
            Criar canal
          </Button>
        </DialogFooter>
      </form>
    </dialogcontent>
  </Dialog>
</template>
