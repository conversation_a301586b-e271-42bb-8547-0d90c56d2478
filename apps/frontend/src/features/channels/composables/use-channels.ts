import {createGlobalState} from "@vueuse/core";
import {useChannel<PERSON>pi} from "@/api/channel.ts";
import {useRouter} from "vue-router";
import {computed} from "vue";
import {useRouteParams} from "@vueuse/router";
import {z} from "zod";


export const formSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  is_private: z.boolean().default(false).optional(),
  workspace_id: z.string(),
  description: z.string().nullish(),
})

export type FormValues = z.infer<typeof formSchema>

export const useChannels = createGlobalState(() => {
  const channelsApi = useChannelApi()
  const router = useRouter()

  const channelId = useRouteParams("channelId", String)
  const workspaceId = useRouteParams("workspaceId", String)

  const {data, isLoading} = channelsApi.useQueryChannels(workspaceId)

  const channels = computed(() => data.value?.items ?? [])

  const currentChannel = computed(() =>
    channels.value?.find(c => c.id === channelId.value) ?? null
  )

  const createChannelMutation = channelsApi.useMutationCreateChannel()

  async function createChannel(data: FormValues) {
    try {
      return await createChannelMutation.mutateAsync(data)
    } catch (e) {
      return null
    }
  }

  async function viewChannel(id: string) {
    await router.push(`/app/${workspaceId.value}/${id}`)
  }

  return {
    currentChannelId: channelId,
    currentChannel,
    channels,
    isLoading,

    createChannel,
    viewChannel,
  }
})
