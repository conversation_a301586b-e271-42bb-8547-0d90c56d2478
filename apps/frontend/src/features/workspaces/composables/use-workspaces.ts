import {createGlobalState} from "@vueuse/core";
import {computed} from "vue";
import {useRouteParams} from "@vueuse/router";
import {useWorkspacesApi} from "@/api/workspace.ts";
import {useRouter} from "vue-router";


export const useWorkspaces = createGlobalState(() => {
  const router = useRouter()
  const workspaceId = useRouteParams("workspaceId", String);
  const workspacesApi = useWorkspacesApi()

  const {data, isLoading} = workspacesApi.useQueryWorkspaces();

  const workspaces = computed(() => data.value?.items ?? [])
  const workspacesCount = computed(() => data.value?.total ?? 0)

  const currentWorkspace = computed(() =>
    workspaces.value?.find(w => w.id === workspaceId.value) ?? null
  );

  const createWorkspace = workspacesApi.useMutationCreateWorkspace();
  const updateWorkspace = workspacesApi.useMutationUpdateWorkspace();

  async function viewWorkspace(id: string) {
    await router.push(`/app/${id}/`);
  }

  return {
    // State
    workspaces,
    workspacesCount,
    isLoading,
    currentWorkspace,
    currentWorkspaceId: workspaceId,

    // Actions
    createWorkspace,
    updateWorkspace,
    viewWorkspace,
  };
});
