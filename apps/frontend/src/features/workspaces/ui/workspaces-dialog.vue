<script lang='ts' setup>
import {computed, ref, watch} from 'vue'
import {useForm} from 'vee-validate'
import {toTypedSchema} from '@vee-validate/zod'

import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from '@/components/ui/dialog'
import {Button} from '@/components/ui/button'
import {Input} from '@/components/ui/input'
import {Textarea} from '@/components/ui/textarea'
import {FormControl, FormField, FormItem, FormLabel,} from '@/components/ui/form'
import {Loader2, PlusIcon} from "lucide-vue-next";
import {useProfile} from "@/api/auth.ts";
import WorkspacesIcon from "@/features/workspaces/ui/workspaces-icon.vue";
import {useWorkspaces} from "@/features/workspaces/composables/use-workspaces.ts";
import {Toolt<PERSON>, TooltipContent, TooltipTrigger} from "@/components/ui/tooltip";
import {createWorkspaceSchema} from "@/api/workspace.ts";
import {WorkspaceOutputDto} from "@coorpe/api/api";

const props = defineProps<{
  workspace?: WorkspaceOutputDto | null
}>()

const emits = defineEmits<{
  close: []
}>()

const open = ref(false)
const imageFile = ref<File | null>(null)
const {user} = useProfile()
const {createWorkspace, updateWorkspace, viewWorkspace} = useWorkspaces()

const form = useForm({
  validationSchema: toTypedSchema(createWorkspaceSchema),
  validateOnMount: false,
  initialValues: {
    name: `Workspace de ${user?.value?.name}`,
    description: null,
  }
})

watch(() => props.workspace, (workspace) => {
  if (!workspace) {
    form.resetForm()
    imageFile.value = null
    return
  }

  form.setValues({
    name: workspace.name,
    description: workspace.description,
  })
}, {immediate: true})

const isLoading = computed(() => {
  return createWorkspace.isPending || updateWorkspace.isPending
})

const handleSubmit = form.handleSubmit(async (data) => {
  try {
    if (imageFile.value) {
      data.icon = imageFile.value
    }

    if (props.workspace) {
      await updateWorkspace.mutateAsync({
        ...data,
        id: props.workspace.id,
      })
    } else {
      const result = await createWorkspace.mutateAsync(data)

      if (!!result) {
        await viewWorkspace(result.id)
      }
    }
    emits('close')
    open.value = false
  } catch (e) {
    console.error(e)
  }
})

</script>

<template>
  <Dialog v-model:open="open">
    <Tooltip>
      <DialogTrigger as-child>
        <TooltipTrigger as-child>
          <Button
            class="size-12"
            size="icon"
            variant="ghost"
          >
            <PlusIcon class="size-6"/>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          Criar novo workspace
        </TooltipContent>
      </DialogTrigger>
    </Tooltip>

    <DialogContent class="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>
          {{ workspace ? 'Editar workspace' : 'Criar workspace' }}
        </DialogTitle>
      </DialogHeader>

      <WorkspacesIcon @change="file => imageFile = file"/>

      <form class="grid gap-4 py-4" @submit.prevent="handleSubmit">
        <FormField v-slot="{ componentField }" name="name">
          <FormItem>
            <FormLabel>Nome</FormLabel>
            <FormControl>
              <Input
                placeholder="Digite o nome do workspace"
                v-bind="componentField"
              />
            </FormControl>
          </FormItem>
        </FormField>

        <FormField v-slot="{ componentField }" name="description">
          <FormItem>
            <FormLabel>Descrição</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Descreva seu workspace..."
                v-bind="componentField"
              />
            </FormControl>
          </FormItem>
        </FormField>

        <DialogFooter>
          <Button :disabled="isLoading.value" type="submit">
            <template v-if="isLoading.value">
              <Loader2 class="mr-2 h-4 w-4 animate-spin"/>
              Carregando
            </template>
            <template v-else>
              {{ workspace ? 'Salvar' : 'Criar workspace' }}
            </template>
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>
