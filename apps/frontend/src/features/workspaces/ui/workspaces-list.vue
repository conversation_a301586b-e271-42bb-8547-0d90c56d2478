<script lang="ts" setup>

import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar";
import {useWorkspaces} from "@/features/workspaces/composables/use-workspaces.ts";
import {Tooltip, TooltipContent, TooltipTrigger} from "@/components/ui/tooltip";
import {ScrollArea} from "@/components/ui/scroll-area";
import {getInitials} from "@/helpers/helpers.ts";

const {workspaces, currentWorkspace, isLoading, viewWorkspace} = useWorkspaces();

</script>

<template>
  <ScrollArea v-if="!isLoading" class="h-full flex-1 min-h-0">
    <div class="flex flex-col items-center gap-2">
      <Tooltip v-for="workspace in workspaces">
        <TooltipTrigger asChild>
          <button
            :key="workspace.id"
            class="size-12 relative cursor-pointer"
            @click="viewWorkspace(workspace.id)"
          >
            <div
              v-if="currentWorkspace?.id === workspace.id"
              class="absolute -left-3 top-1/2 -translate-y-1/2 h-5 w-2 bg-accent-foreground rounded-full z-10"
            />
            <Avatar class="size-full rounded-xl">
              <AvatarImage
                v-if="workspace.iconUrl"
                :alt="workspace.name"
                :src="workspace.iconUrl"
                class="object-cover"
              />
              <AvatarFallback class="bg-primary/10 rounded-xl">
                {{ getInitials(workspace.name) }}
              </AvatarFallback>
            </Avatar>
          </button>
        </TooltipTrigger>
        <TooltipContent>
          {{ workspace.name }}
        </TooltipContent>
      </Tooltip>
    </div>
  </ScrollArea>
</template>

