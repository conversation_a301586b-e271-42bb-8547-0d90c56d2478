<script lang="ts" setup>
import {Camera} from "lucide-vue-next";
import {useDropZone, useFileDialog, useObjectUrl} from "@vueuse/core";
import {shallowRef, useTemplateRef, watch} from "vue";

const emits = defineEmits<{
  change: [file: File | null]
}>()

const dropzoneRef = useTemplateRef<HTMLElement>('dropzoneRef')
const fileRef = shallowRef()
const objectUrl = useObjectUrl(fileRef)

const {open, onChange} = useFileDialog({
  accept: 'image/*',
  multiple: false,
  initialFiles: [],
})

const {isOverDropZone, files} = useDropZone(dropzoneRef, {
  multiple: false,
  preventDefaultForUnhandled: true
})

onChange((files) => {
  fileRef.value = files?.[0]
  emits('change', fileRef.value)
})

watch(files, (files) => {
  fileRef.value = files?.[0]
  emits('change', fileRef.value)
})

</script>

<template>
  <div
    ref="dropzoneRef"
    :class="['rounded-full cursor-pointer flex mx-auto gap-4 size-16 items-center justify-center transition-colors',
               !objectUrl && 'border border-dashed hover:border-primary cursor-pointer',
               isOverDropZone && 'border border-dashed border-primary']"
    :style="objectUrl ? {
        backgroundImage: `url(${objectUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      } : {}"
    @click="() => open()"
  >
    <Camera v-if="!objectUrl"/>
  </div>
</template>
