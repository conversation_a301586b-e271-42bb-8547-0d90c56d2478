<script lang="ts" setup>
import {Button} from "@/components/ui/button";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {Popover, PopoverContent, PopoverTrigger,} from "@/components/ui/popover";
import {UsersIcon} from "lucide-vue-next";
import {useChannels} from "@/features/channels/composables/use-channels.ts";
import {useChannelPopover} from "@/features/channels-members/composables/use-channel-popover.ts";
import {useChannelMembersApi} from "@/api/channel-members.ts";
import {Skeleton} from "@/components/ui/skeleton";
import ChannelMembersDialog from "@/features/channels-members/ui/channel-members-dialog.vue";
import {ref} from "vue";

const {open} = useChannelPopover();
const {currentChannel, currentChannelId} = useChannels();

const dialogOpen = ref(false);
const channelMembersApi = useChannelMembersApi();

const {data, isLoading} = channelMembersApi.useQueryMembers({
  channelId: currentChannelId.value,
  limit: 10,
});
</script>

<template>
  <Popover v-model:open="open">
    <PopoverTrigger as-child>
      <Button class="w-[50px] justify-start" size="sm" variant="outline">
        <UsersIcon class="h-4 w-4"/>
        {{ currentChannel?.members_count }}
      </Button>
    </PopoverTrigger>
    <PopoverContent align="start" class="p-0" side="right">
      <Command>
        <CommandInput placeholder="Buscar membros do canal..."/>
        <CommandList class="relative">
          <CommandEmpty>Nenhum membro encontrado.</CommandEmpty>
          <CommandGroup
            v-if="!isLoading"
            :heading="`Membros (${currentChannel?.members_count})`"
            class="space-y-2"
          >
            <div class="overflow-y-scroll max-h-[160px]">
              <CommandItem
                v-for="member in data?.items"
                :key="member.id"
                :value="member.name"
              >
                <img
                  v-if="member.avatarUrl"
                  :alt="member.name"
                  :src="member.avatarUrl"
                  class="size-10 rounded-full mr-2"
                />
                <span>{{ member.name }}</span>
              </CommandItem>
            </div>
          </CommandGroup>
          <Skeleton v-if="isLoading" class="h-12 m-2"/>
          <Skeleton v-if="isLoading" class="h-12 m-2"/>
        </CommandList>
        <CommandList class="relative">
          <CommandGroup
            class="space-y-2 overflow-y-scroll"
          >
            <CommandSeparator/>
            <CommandItem value="addPeople" @select="dialogOpen = true">
              <div class="flex items-center gap-2">
                <UsersIcon class="h-4 w-4 mr-2"/>
                Adicionar pessoas
              </div>
            </CommandItem>
          </CommandGroup>
        </CommandList>
      </Command>
    </PopoverContent>
  </Popover>

  <ChannelMembersDialog v-model:open="dialogOpen"
                        description="Você também pode adicionar endereços de e-mail de pessoas que não são membros do canal"
                        title="Adicionar pessoas"/>
</template>
