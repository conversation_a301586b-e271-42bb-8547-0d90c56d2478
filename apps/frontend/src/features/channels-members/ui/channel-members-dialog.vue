<script lang="ts" setup>
import {Button} from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {Input} from '@/components/ui/input'
import {FormControl, FormField, FormItem, FormLabel} from "@/components/ui/form";
import {clsx} from "clsx";

defineProps<{
  open: boolean
  title: string
  description?: string
  saveDisabled?: boolean
  className?: string
}>()

defineEmits<{
  'update:open': [value: boolean]
  'save': []
}>()

</script>

<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent :class="clsx('sm:max-w-[425px]', className)">
      <DialogHeader>
        <DialogTitle>{{ title }}</DialogTitle>
        <DialogDescription>
          {{ description }}
        </DialogDescription>
      </DialogHeader>
      <div class="grid gap-4 py-4">
        <FormField v-slot="{ componentField }" name="name">
          <FormItem>
            <FormLabel>Email</FormLabel>
            <FormControl>
              <Input
                placeholder="Digite o e-mail da pessoa (e.g. '<EMAIL>')"
                v-bind="componentField"
              />
            </FormControl>
          </FormItem>
        </FormField>
      </div>
      <DialogFooter>
        <Button :disabled="saveDisabled" type="submit">
          Adicionar
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
