<script lang="ts" setup>
import {But<PERSON>} from "@/components/ui/button";
import MessageSkeleton from "@/features/channel-messages/ui/message-skeleton.vue";
import {useVirtualizer} from "@tanstack/vue-virtual";
import {useMediaQuery, watchThrottled} from "@vueuse/core";
import {ArrowDownToLine, Loader2} from "lucide-vue-next";
import {computed, nextTick, onMounted, ref, useTemplateRef, watch, watchEffect} from "vue";
import Message from "./message.vue";
import {MessageOutputDto} from "@coorpe/api/api"
import {useMessages, useMessagesSubscription} from "@/api/channel-messages.ts";
import {useChatMessagesFilters} from "@/features/channel-messages/composables/use-filters.ts";

const filters = useChatMessagesFilters();

const {data, isFetchingNextPage, fetchNextPage, hasNextPage, isPending, refetch} =
  useMessages(filters.computedFilters);
const subscription = useMessagesSubscription(filters.channelId);

const messages = ref<MessageOutputDto[]>([]);

onMounted(() => {
  refetch();
})

watch(data, (v) => {
  if (!v) return;
  messages.value = v.pages.flatMap((d) => d.items);
}, {immediate: true});

const totalMessages = computed(() => messages.value.length);

const parentRef = useTemplateRef("parentRef");
const isAutoScrolling = ref(true);

const rowVirtualizer = useVirtualizer({
  get count() {
    return totalMessages.value;
  },
  getScrollElement: () => parentRef.value,
  estimateSize: () => 42,
  overscan: 5,
});

const virtualRows = computed(() => rowVirtualizer.value.getVirtualItems());
const totalSize = computed(() => rowVirtualizer.value.getTotalSize());

watch(subscription.data, (v) => {
  if (!v) return;
  messages.value.push(v);

  if (isAutoScrolling.value) {
    scrollToBottom();
  }
});

watchEffect(() => {
  const [lastItem] = [...virtualRows.value].reverse();

  if (!lastItem) {
    return;
  }

  if (lastItem.index >= totalMessages.value - 1 && hasNextPage.value && !isFetchingNextPage.value) {
    fetchNextPage();
  }
});

function scrollToBottom() {
  rowVirtualizer.value.scrollToIndex(totalMessages.value - 1, {align: "end"});

  if (!isAutoScrolling.value) {
    isAutoScrolling.value = true;
  }
}

watch(messages, () => {
  nextTick(scrollToBottom);
}, {once: true});

watchThrottled(
  rowVirtualizer,
  (v) => {
    const currentScrollPosition = (v.scrollRect?.height || 0) + (v.scrollOffset || 0);

    if (v.isScrolling && currentScrollPosition > (v.scrollOffset ?? 0)) {
      isAutoScrolling.value = false;
    }

    if (currentScrollPosition >= totalSize.value - 500) {
      isAutoScrolling.value = true;
    }
  },
  {throttle: 500}
);

function measureElement(el: HTMLDivElement) {
  if (!el) {
    return;
  }

  rowVirtualizer.value.measureElement(el);
  return undefined;
}

const isLargeScreen = useMediaQuery("(min-width: 1024px)");
</script>

<template>
  <div class="flex flex-col h-[calc(100vh-26rem)] relative">
    <div ref="parentRef" class="overflow-y-auto h-full [contain:strict] [overflow-anchor:none]">
      <MessageSkeleton :count="isLargeScreen ? 3 : 1" :loading="isPending"/>
      <div
        :style="{
          height: `${totalSize}px`,
          width: '100%',
          position: 'relative',
        }"
      >
        <div
          :style="{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            transform: `translateY(${virtualRows[0]?.start ?? 0}px)`,
          }"
        >
          <div
            v-for="virtualRow in virtualRows"
            :key="virtualRow.index"
            :ref="measureElement"
            :data-index="virtualRow.index"
          >
            <Message :message="messages[virtualRow.index]"/>
          </div>
        </div>
      </div>
      <div v-if="isFetchingNextPage && hasNextPage" class="flex justify-center p-6">
        <Loader2 class="w-4 h-4 animate-spin"/>
      </div>
    </div>
    <div v-if="!isAutoScrolling" class="absolute z-10 inset-x-0 bottom-3 w-fit mx-auto">
      <Button variant="secondary" @click="scrollToBottom">
        Avançar para mensagens recentes
        <ArrowDownToLine/>
      </Button>
    </div>
  </div>
</template>
