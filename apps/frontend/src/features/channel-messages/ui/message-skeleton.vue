<script lang="ts" setup>
import {Skeleton} from "@/components/ui/skeleton";

defineProps<{
  loading: boolean
  count: number
}>()

const getRandomWidth = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1) + min) + 'px';
}

</script>

<template>
  <div v-if="loading" class="overflow-y-hidden space-y-4 p-6">
    <div v-for="i in count" :key="i" class="flex gap-2">
      <Skeleton class="rounded-full size-8 sm:size-16 md:size-20"/>
      <div class="flex-1">
        <div class="flex flex-col gap-3">
          <Skeleton
            :style="{ width: getRandomWidth(250, 500) }"
            class="h-64 w-full rounded-md"
          />
          <Skeleton
            v-for="line in Math.floor(Math.random() * 2) + 3"
            :key="line"
            :style="{ width: getRandomWidth(200, 500) }"
            class="h-4 rounded-md"
          />
        </div>
      </div>
    </div>
  </div>
</template>
