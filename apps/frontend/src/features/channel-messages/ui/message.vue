<script lang="ts" setup>
import {useProfile} from "@/api/auth.ts";
import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar";
import {cn} from "@/lib/utils.ts";
import {computed} from "vue";
import {MessageOutputDto} from "@coorpe/api/api";
import {fdn, getInitials} from "@/helpers/helpers.ts";

const props = defineProps<{
  message: MessageOutputDto;
}>();
const {user} = useProfile();

const isSender = computed(() => {
  return props.message.senderId === user.value?.id;
});

</script>

<template>
  <div
    :class="cn('flex items-start px-6 py-2', isSender ? 'justify-end' : 'justify-start')">
    <div class="flex flex-col items-end">
      <span class="text-sm text-muted-foreground">{{ fdn(message.createdAt) }}</span>

      <div class="flex gap-4">
        <Avatar class="h-9 w-9" v-if="!isSender">
          <AvatarImage :alt="message.senderName" :src="message.senderAvatar"/>
          <AvatarFallback>{{ getInitials(message.senderName ?? '') }}</AvatarFallback>
        </Avatar>
        <div class="flex flex-col px-6 py-2 bg-primary/10 rounded-lg">
          <div class="flex items-baseline space-x-2" v-if="!isSender">
            <span class="font-semibold text-sm">{{ message.senderName }}</span>
          </div>
          <div class="text-sm text-foreground break-words leading-snug">
            <article class="prose prose-sm dark:prose-invert" v-html="message.body"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
