import {createGlobalState} from '@vueuse/core'
import {computed, ref} from 'vue'
import {GetChannelMessagesInput} from "@/api/channel-messages.ts";
import {useRouteParams} from "@vueuse/router";

export const useChatMessagesFilters = createGlobalState(() => {
  const channelId = useRouteParams("channelId", String);
  const page = ref(0)
  const limit = ref(50)

  const computedFilters = computed<GetChannelMessagesInput>(() => {
    return {
      page: page.value,
      limit: limit.value,
      channelId: channelId.value,
    }
  })

  return {
    channelId,
    computedFilters,
    page,
    limit,
  }
})
