<script setup lang="ts">

import {removeHTMLTags} from "@/helpers/helpers.ts";
import {<PERSON><PERSON>} from "@/components/ui/button";
import {FormControl, FormField, FormItem} from "@/components/ui/form";
import {SendHorizontal} from "lucide-vue-next";
import Editor from "@/components/editor/editor.vue";
import {sendMessageSchema, useMutationSendMessage} from "@/api/channel-messages.ts";
import {useRouteParams} from "@vueuse/router";
import {useForm} from "vee-validate";
import {toTypedSchema} from "@vee-validate/zod";
import {SendMessageInputDtoTypeEnum} from "@coorpe/api/api";

const sendMessage = useMutationSendMessage()

const workspaceId = useRouteParams("workspaceId", String);
const channelId = useRouteParams("channelId", String);

const form = useForm({
  validationSchema: toTypedSchema(sendMessageSchema),
  validateOnMount: false,
  initialValues: {
    body: '',
    workspaceId: workspaceId.value,
    channelId: channelId.value,
    conversationId: null,
    type: SendMessageInputDtoTypeEnum.Text
  },
})

const handleSubmit = form.handleSubmit(async (values) => {
  try {
    await sendMessage.mutateAsync(values)
    form.resetForm()
  } catch (error) {
    console.error(error)
  }
})
</script>

<template>
  <form class="relative m-6" @submit.prevent="handleSubmit">
    <FormField v-slot="{ componentField, value }" name="body">
      <FormItem>
        <FormControl>
          <Editor v-bind="componentField" @submit="handleSubmit"/>

          <div class="absolute bottom-2 right-2">
            <Button
              :disabled="!removeHTMLTags(value)"
              size="sm"
              type="button"
              variant="secondary"
              @click="handleSubmit"
            >
              <SendHorizontal class="size-6 text-muted-foreground"/>
            </Button>
          </div>
        </FormControl>
      </FormItem>
    </FormField>
  </form>
</template>
