import {createGlobalState} from "@vueuse/core";
import {useMutation, useQuery, useQueryClient} from "@tanstack/vue-query";
import {MaybeRef, unref} from "vue";
import {CreateChannelOutputDto, GetChannelsOutputDto} from "@coorpe/api/api";
import {api} from "@/api/api.ts";

const channelsInvalidationKey = "channelsInvalidationKey";

export const useChannelApi = createGlobalState(() => {
  const queryClient = useQueryClient()

  const useQueryChannels = (workspaceId: MaybeRef<string | null>) => useQuery<GetChannelsOutputDto>({
    queryKey: [channelsInvalidationKey, workspaceId],
    queryFn: async ({queryKey}) => {
      const [, id] = queryKey

      const response = await api.getChannels(id as string)
      return response.data;
    },
    enabled: !!unref(workspaceId),
  })

  const useMutationCreateChannel = () => useMutation({
    mutationKey: [channelsInvalidationKey],
    mutationFn: async (data: CreateChannelOutputDto) => {
      const response = await api.createChannel(data)
      return response.data;
    },
    async onSuccess() {
      await queryClient.invalidateQueries({queryKey: [channelsInvalidationKey]})
    },
  })

  return {
    useQueryChannels,

    useMutationCreateChannel,
  }
})
