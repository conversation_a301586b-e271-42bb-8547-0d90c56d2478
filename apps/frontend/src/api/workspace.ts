import {useMutation, useQuery, useQueryClient} from "@tanstack/vue-query";
import {createGlobalState} from "@vueuse/core";
import {api} from "@/api/api.ts";
import {z} from "zod";

const workspacesInvalidationKey = "workspacesInvalidationKey";

export const updateWorkspaceSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Nome é obrigatório"),
  description: z.string().nullable(),
  icon: z.instanceof(File).optional(),
})

export const createWorkspaceSchema = updateWorkspaceSchema.omit({id: true})

export type UpdateWorkspaceInput = z.infer<typeof updateWorkspaceSchema>
export type CreateWorkspaceInput = z.infer<typeof createWorkspaceSchema>

export const useWorkspacesApi = createGlobalState(() => {
  const queryClient = useQueryClient();

  const useQueryWorkspaces = () =>
    useQuery({
      queryKey: [workspacesInvalidationKey],
      queryFn: async () => {
        const response = await api.getAllWorkspaces()
        return response.data;
      },
    });

  const useQueryWorkspace = (id: string) =>
    useQuery({
      queryKey: [workspacesInvalidationKey, id],
      queryFn: async () => {
        const response = await api.getWorkspaceById(id)
        return response.data;
      },
      enabled: !!id,
    });

  const useMutationCreateWorkspace = () =>
    useMutation({
      mutationKey: [workspacesInvalidationKey],
      mutationFn: async (data: CreateWorkspaceInput) => {
        const response = await api.createWorkspace(data)
        return response.data;
      },
      async onSuccess() {
        await queryClient.invalidateQueries({queryKey: [workspacesInvalidationKey]});
      },
    });

  const useMutationUpdateWorkspace = () =>
    useMutation({
      mutationKey: [workspacesInvalidationKey],
      mutationFn: async (data: UpdateWorkspaceInput) => {
        const response = await api.updateWorkspace(data.id, {
          name: data.name,
          description: data.description,
        })
        return response.data;
      },
      async onSuccess(data) {
        await queryClient.invalidateQueries({queryKey: [workspacesInvalidationKey, data.id]});
      },
    });

  return {
    useQueryWorkspaces,
    useQueryWorkspace,

    useMutationCreateWorkspace,
    useMutationUpdateWorkspace,
  };
});
