import {createGlobalState} from "@vueuse/core";
import {useQuery} from "@tanstack/vue-query";
import {api} from "@/api/api.ts";
import {computed, MaybeRef, unref} from "vue";

const workspaceMembersInvalidationKey = 'workspaceMembersInvalidationKey';

export const useWorkspaceMembersApi = createGlobalState(() => {
  const useQueryMembers = (id: MaybeRef<string | null>) => useQuery({
    queryKey: [workspaceMembersInvalidationKey, id],
    queryFn: async ({queryKey}) => {
      const [, id] = queryKey

      const response = await api.getWorkspaceMembers(id as string);
      return response.data;
    },
    enabled: computed(() => !!unref(id)),
  })

  return {
    useQueryMembers,
  }
})
