import {createGlobalState} from "@vueuse/core";
import {api} from "@/api/api.ts";
import {useMutation} from "@tanstack/vue-query";
import {reactive} from 'vue'

const mailInvalidationKey = "mailInvalidationKey";

export const useMailAuthenticationApi = createGlobalState(() => {
  const useMutationSendCode = () => reactive(useMutation({
    mutationKey: [mailInvalidationKey],
    mutationFn: async (email: string) => {
      return api.sendEmailCode({email})
    },
  }));

  const useMutationVerifyCode = () => reactive(useMutation({
    mutationKey: [mailInvalidationKey],
    mutationFn: async (code: string) => {
      return api.verifyEmailCode({code})
    },
  }));

  return {
    useMutationSendCode,
    useMutationVerifyCode
  };
})
