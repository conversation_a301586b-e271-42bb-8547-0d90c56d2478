import {Extension} from "@tiptap/vue-3";

export interface KeyboardHandlerOptions {
  onSubmit: () => void;
  ctrlSend: boolean;
  codeBlockOnCtrlEnter: boolean;
}

export const KeyboardHandler = Extension.create<KeyboardHandlerOptions>({
  name: 'keyboardHandler',

  // Set default values for options
  addOptions() {
    return {
      onSubmit: () => {
      },
      ctrlSend: false,
      codeBlockOnCtrlEnter: true,
    };
  },

  addKeyboardShortcuts() {
    return {
      'Enter': () => {
        const isCodeBlockActive = this.editor.isActive('codeBlock');
        const isBulletListActive = this.editor.isActive('bulletList');
        const isOrderedListActive = this.editor.isActive('orderedList');

        if (isCodeBlockActive ||
          isBulletListActive ||
          isOrderedListActive ||
          this.options.ctrlSend) {
          return false;
        }

        this.options.onSubmit();
        return this.editor.commands.clearContent();
      },

      'Mod-Enter': () => {
        const isCodeBlockActive = this.editor.isActive('codeBlock');

        if (isCodeBlockActive && this.options.codeBlockOnCtrlEnter) {
          this.options.onSubmit();
          return this.editor.commands.clearContent();
        }

        if (!isCodeBlockActive && this.options.ctrlSend) {
          this.options.onSubmit();
          return this.editor.commands.clearContent();
        }

        return false;
      },

      'Shift-Enter': () => {
        return this.editor.commands.first(({commands}) => [
          () => commands.newlineInCode(),
          () => commands.createParagraphNear(),
          () => commands.liftEmptyBlock(),
          () => commands.splitBlock(),
        ]);
      },
    };
  },
});
