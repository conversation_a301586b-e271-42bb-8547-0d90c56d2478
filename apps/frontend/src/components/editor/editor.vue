<script lang="ts" setup>
import {EditorContent, useEditor} from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import Toolbar from "@/components/editor/toolbar.vue";
import {KeyboardHandler} from "@/components/editor/extensions/keyboard-handler.ts";

const props = defineProps<{
  modelValue?: string
  name?: string
}>()

const emit = defineEmits(['update:modelValue', 'submit'])

const editor = useEditor({
  content: props.modelValue,
  onUpdate: ({editor}) => {
    emit('update:modelValue', editor.getHTML())
  },
  extensions: [
    StarterKit,
    Placeholder.configure({
      placeholder: 'Escreva algo...',
    }),
    KeyboardHandler.configure({
      onSubmit: () => emit('submit')
    })
  ],
  editorProps: {
    attributes: {
      spellcheck: 'false',
      class:
        'border bg-primary-foreground rounded-b-lg p-4 min-h-[12rem] max-h-[12rem] overflow-y-auto outline-none max-w-none',
    },
  },
})

</script>

<template>
  <div class="relative">
    <Toolbar :editor="editor"/>
    <EditorContent :editor="editor" :name="name"/>
  </div>
</template>

<style>

.tiptap {
  :first-child {
    margin-top: 0;
  }

  /* Code block styles */

  pre {
    background: var(--muted);
    border-radius: 0.5rem;
    color: var(--color-white);
    font-family: 'JetBrains Mono', monospace;
    margin: 1.5rem 0;
    padding: 0.75rem 1rem;

    code {
      background: none;
      color: inherit;
      font-size: 0.8rem;
      padding: 0;
    }
  }

  /* List styles */

  ul,
  ol {
    padding: 0 1rem;
    margin: 1.25rem 1rem 1.25rem 0.4rem;

    li p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  ul {
    list-style: initial;
  }

  ol {
    list-style: decimal;
  }

  /* Heading styles */

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    line-height: 1.1;
    margin-top: 2.5rem;
    text-wrap: pretty;
  }

  h1,
  h2 {
    margin-top: 3.5rem;
    margin-bottom: 1.5rem;
  }

  h1 {
    font-size: 1.4rem;
  }

  h2 {
    font-size: 1.2rem;
  }

  h3 {
    font-size: 1.1rem;
  }

  h4,
  h5,
  h6 {
    font-size: 1rem;
  }

  /* Placeholder (at the top) */

  p.is-editor-empty:first-child::before {
    color: var(--muted-foreground);
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }
}
</style>

