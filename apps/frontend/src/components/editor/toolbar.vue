<script lang="ts" setup>
import {
  Bold as BoldIcon,
  CodeXml as CodeIcon,
  Heading,
  Italic as ItalicIcon,
  List as ListIcon,
  ListOrdered as OrderedListIcon,
  Minus as HorizontalRuleIcon,
  Redo2 as RedoIcon,
  Undo2 as UndoIcon
} from "lucide-vue-next";
import {Editor} from "@tiptap/vue-3";
import {Toggle} from "@/components/ui/toggle";

defineProps<{ editor?: Editor }>();

</script>

<template>
  <section
    v-if="editor"
    class="flex items-center flex-wrap rounded-t-lg bg-primary-foreground text-muted-foreground gap-x-2 border-t border-l border-r p-2"
  >
    <Toggle
      :model-value="editor.isActive('bold')"
      @update:modelValue="() => editor?.chain().focus().toggleBold().run()"
    >
      <BoldIcon class="size-6"/>
    </Toggle>
    <Toggle
      :model-value="editor.isActive('italic')"
      @update:modelValue="() => editor?.chain().focus().toggleItalic().run()"
    >
      <ItalicIcon class="size-6"/>
    </Toggle>
    <Toggle
      :model-value="editor.isActive('heading', { level: 1 })"
      @update:modelValue="() => editor?.chain().focus().toggleHeading({ level: 1 }).run()"
    >
      <Heading class="size-6"/>
    </Toggle>
    <Toggle
      :model-value="editor.isActive('bulletList')"
      @update:modelValue="() => editor?.chain().focus().toggleBulletList().run()"
    >
      <ListIcon class="size-6"/>
    </Toggle>
    <Toggle
      :model-value="editor.isActive('orderedList')"
      @update:modelValue="() => editor?.chain().focus().toggleOrderedList().run()"
    >
      <OrderedListIcon class="size-6"/>
    </Toggle>
    <Toggle
      :model-value="editor.isActive('codeBlock')"
      @update:modelValue="() => editor?.chain().focus().toggleCodeBlock().run()"
    >
      <CodeIcon class="size-6"/>
    </Toggle>
    <Toggle
      :model-value="false"
      @update:modelValue="() => editor?.chain().focus().setHorizontalRule().run()"
    >
      <HorizontalRuleIcon class="size-6"/>
    </Toggle>
    <Toggle
      :model-value="false"
      @update:modelValue="() => editor?.chain().focus().undo().run()"
    >
      <UndoIcon class="size-6"/>
    </Toggle>
    <Toggle
      :model-value="false"
      @update:modelValue="() => editor?.chain().focus().redo().run()"
    >
      <RedoIcon class="size-6"/>
    </Toggle>
  </section>
</template>
