<script lang="ts" setup>
import {cn} from '@/lib/utils'
import {ErrorMessage} from 'vee-validate'
import {type HTMLAttributes, toValue} from 'vue'
import {useFormField} from './useFormField'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()

const {name, formMessageId} = useFormField()
</script>

<template>
  <ErrorMessage
    :id="formMessageId"
    :class="cn('text-destructive text-xs italic', props.class)"
    :name="toValue(name)"
    as="p"
    data-slot="form-message"
  />
</template>
