<script lang="ts" setup>
import {cn} from '@/lib/utils'
import {ToastRoot, type ToastRootEmits, useForwardPropsEmits} from 'reka-ui'
import {computed} from 'vue'
import {type ToastProps, toastVariants} from '.'

const props = defineProps<ToastProps>()

const emits = defineEmits<ToastRootEmits>()

const delegatedProps = computed(() => {
  const {class: _, ...delegated} = props

  return delegated
})

const forwarded = useForwardPropsEmits(delegatedProps, emits)
</script>

<template>
  <ToastRoot
    :class="cn(toastVariants({ variant }), props.class)"
    v-bind="forwarded"
    @update:open="onOpenChange"
  >
    <slot/>
  </ToastRoot>
</template>
