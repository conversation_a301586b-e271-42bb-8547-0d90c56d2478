<script lang="ts" setup>
import type {HTMLAttributes} from 'vue'
import {cn} from '@/lib/utils'
import {Primitive, type PrimitiveProps} from 'reka-ui'
import {type ButtonVariants, buttonVariants} from '.'

interface Props extends PrimitiveProps {
  variant?: ButtonVariants['variant']
  size?: ButtonVariants['size']
  class?: HTMLAttributes['class']
}

const props = withDefaults(defineProps<Props>(), {
  as: 'button',
})
</script>

<template>
  <Primitive
    :as="as"
    :as-child="asChild"
    :class="cn(buttonVariants({ variant, size, class: 'cursor-pointer' }), props.class)"
    data-slot="button"
  >
    <slot/>
  </Primitive>
</template>
