<script lang="ts" setup>
import type {HTMLAttributes} from 'vue'
import {cn} from '@/lib/utils'
import {reactiveOmit} from '@vueuse/core'
import {Toggle as RekaToggle, type ToggleProps} from 'reka-ui'
import {type ToggleVariants, toggleVariants} from '.'

const props = withDefaults(defineProps<ToggleProps & {
  class?: HTMLAttributes['class']
  variant?: ToggleVariants['variant']
  size?: ToggleVariants['size']
}>(), {
  variant: 'default',
  size: 'default',
  disabled: false,
})

const emit = defineEmits(['update:modelValue'])

const delegatedProps = reactiveOmit(props, 'class', 'size', 'variant')
</script>

<template>
  <RekaToggle
    :class="cn(toggleVariants({ variant, size }), props.class)"
    data-slot="toggle"
    v-bind="delegatedProps"
    @update:modelValue="val => emit('update:modelValue', val)"
  >
    <slot/>
  </RekaToggle>
</template>
