import {z} from "zod";

const customErrors: z.ZodErrorMap = (issue, ctx) => {
  switch (issue.code) {
    case "invalid_type":
      if (issue.received === "undefined") {
        return {message: "Este campo é obrigatório."};
      }
      break;

    case "invalid_string":
      if (issue.validation === "email") {
        return {message: "Email inválido."};
      }
      break;
  }
  return {message: ctx.defaultError};
}

z.setErrorMap(customErrors);

