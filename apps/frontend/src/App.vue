<script lang="ts" setup>
import {ref} from "vue";
import {RouterView, useRouter} from "vue-router";
import {Loader2} from "lucide-vue-next";

const isRouterReady = ref(false);
const router = useRouter();
router.isReady().finally(() => (isRouterReady.value = true));
</script>

<template>
  <div v-if="!isRouterReady"
       class="flex justify-center items-center h-screen">
    <Loader2 class="animate-spin text-primary size-12"/>
  </div>
  <RouterView v-else/>
</template>
