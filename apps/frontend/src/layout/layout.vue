<script lang="ts" setup>
import Sidebar from "@/layout/sidebar/sidebar.vue";
import AppHeader from "@/layout/app-header.vue";
import {useRouter} from "vue-router";
import {ref} from "vue";
import Toaster from '@/components/ui/toast/Toaster.vue'

const isRouterReady = ref(false);
const router = useRouter();

router.isReady().finally(() => (isRouterReady.value = true));


</script>

<template>
  <AppHeader/>
  <Sidebar>
    <RouterView v-slot="{ Component, route }">
      <div
        :key="route.path"
        :style="{
            padding: route.meta?.noPadding ? undefined : '24px',
            height: '100%',
          }"
      >
        <component :is="Component"/>
      </div>
    </RouterView>
    <Toaster/>
  </Sidebar>
</template>
