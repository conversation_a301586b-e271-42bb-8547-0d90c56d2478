<script lang="ts" setup>
import {Input} from "@/components/ui/input";
import {Button} from "@/components/ui/button";
import {Bell, HelpCircle, Plus, Search} from "lucide-vue-next";
import Logo from "@/assets/logo.svg?use";
</script>

<template>
  <header
    class="flex items-center justify-between bg-sidebar-accent px-4 border-b border-border">
    <Logo class="size-12 text-muted-foreground"/>

    <div
      class="flex-1 items-center flex gap-2 mx-4 max-w-xl rounded-md">
      <div class="relative w-full">
        <Input
          class="pl-10 h-9 hover:bg-accent focus-visible:bg-background transition-colors border border-primary/20"
          placeholder="Pesquisar"
          type="text"
        />
        <span class="absolute start-0 inset-y-0 flex items-center justify-center px-2">
          <Search class="size-4 text-muted-foreground"/>
        </span>
      </div>

      <Button size="sm">
        <Plus class="size-5"/>
        Criar
      </Button>
    </div>

    <div class="flex items-center gap-2">
      <Button class="rounded-full" size="icon" variant="ghost">
        <HelpCircle class="size-5 text-muted-foreground"/>
      </Button>
      <Button class="rounded-full" size="icon" variant="ghost">
        <Bell class="size-5 text-muted-foreground"/>
      </Button>
    </div>
  </header>
</template>
