<script lang="ts" setup>
import {ChevronsUpDown, LogOut, MoonIcon, Settings, SunIcon} from 'lucide-vue-next'
import {computed} from 'vue'

import {Avatar, AvatarFallback, AvatarImage} from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import DropdownMenuGroup from '@/components/ui/dropdown-menu/DropdownMenuGroup.vue'
import {SidebarMenuButton, useSidebar} from '@/components/ui/sidebar'
import {useTheme} from '@/composables/use-theme'
import {useAuthApi, useProfile} from "@/api/auth.ts";
import {DropdownMenuContentProps} from "reka-ui";

const authManager = useAuthApi()
const useLogout = authManager.useMutationLogout;
const {user} = useProfile()

const {open: sidebarOpen, isMobile, setOpenMobile} = useSidebar()
const theme = useTheme()
const logout = useLogout()

function toggleMobileSidebar() {
  if (isMobile.value) {
    setOpenMobile(false)
  }
}

const dropdownProps = computed((): DropdownMenuContentProps & { class?: string } => {
  if (sidebarOpen.value) return {
    class: 'w-[--radix-dropdown-menu-trigger-width]',
    side: 'bottom',
    align: 'end',
    sideOffset: 4,
  }

  return {
    class: 'w-[300px]',
    alignOffset: -4,
    align: 'start',
    sideOffset: 12,
    side: 'right',
  }
})
</script>

<template>
  <DropdownMenu v-if="user">
    <DropdownMenuTrigger as-child>
      <SidebarMenuButton
        class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
        size="lg"
      >
        <Avatar class="size-8 rounded-full">
          <AvatarImage :alt="user.email" :src="user.avatarURL ?? ''"/>
          <AvatarFallback class="rounded-lg">
            {{ user.name }}
          </AvatarFallback>
        </Avatar>
        <div class="grid flex-1 text-left text-sm leading-tight">
          <span class="truncate font-semibold">{{ user.name }}</span>
        </div>
        <ChevronsUpDown class="ml-auto size-4"/>
      </SidebarMenuButton>
    </DropdownMenuTrigger>

    <DropdownMenuContent
      class="min-w-56 rounded-lg"
      v-bind="dropdownProps"
    >
      <DropdownMenuGroup>
        <DropdownMenuItem as-child @click="toggleMobileSidebar">
          <RouterLink class="flex items-center" to="/dashboard/settings">
            <Settings class="mr-2 size-4"/>
            Configurações
          </RouterLink>
        </DropdownMenuItem>

        <DropdownMenuItem as-child @select.prevent="theme.toggleTheme">
          <div>
            <template v-if="theme.isDark.value">
              <SunIcon class="mr-2 size-4"/>
              Tema claro
            </template>
            <template v-else>
              <MoonIcon class="mr-2 size-4"/>
              Tema escuro
            </template>
          </div>
        </DropdownMenuItem>

      </DropdownMenuGroup>

      <DropdownMenuSeparator/>

      <DropdownMenuItem @click="logout.mutate()">
        <LogOut class="mr-2 size-4"/>
        Sair
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
