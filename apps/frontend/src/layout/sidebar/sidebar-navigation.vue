<script lang="ts" setup>
import ChannelsCreateDialog from "@/features/channels/ui/channels-create-dialog.vue";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem
} from "@/components/ui/sidebar";
import {useChannels} from "@/features/channels/composables/use-channels.ts";
import {ChartNoAxesGanttIcon, Columns4Icon, GlobeIcon, HashIcon, Rows4Icon} from "lucide-vue-next";
import {computed} from "vue";

const {channels, viewChannel, currentChannelId} = useChannels()

const links = computed(() => {
  return [
    {
      label: "Resumo",
      href: "/summary",
      icon: GlobeIcon,
      disabled: true
    },
    {
      label: "Cronograma",
      href: "/timeline",
      icon: ChartNoAxesGanttIcon,
      disabled: true
    }, {
      label: "Backlog",
      href: "/backlog",
      icon: Rows4Icon,
      disabled: true
    },
    {
      label: "Sprints Ativos",
      href: "/kanban",
      icon: Columns4Icon,
    }
  ]
})

</script>

<template>
  <SidebarGroup class="h-full">
    <SidebarMenu class="flex justify-between h-full">
      <div>
        <SidebarGroupLabel class="flex justify-between my-2">
          Canais
          <ChannelsCreateDialog/>
        </SidebarGroupLabel>
        <SidebarMenuItem v-for="channel in channels" :key="channel.id">
          <SidebarMenuButton
            :is-active="currentChannelId === channel.id"
            as-child
            @click="viewChannel(channel.id)"
          >
            <component>
              <HashIcon/>
              <span>{{ channel.name }}</span>
            </component>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </div>

      <div>
        <SidebarGroupLabel>Planning</SidebarGroupLabel>
        <SidebarMenuItem v-for="link in links" :key="link.label">
          <SidebarMenuButton
            :as="'button'"
            :class="link.disabled && 'opacity-50 pointer-events-none'"
            :is-active="link.href === $route.path"
            as-child
          >
            <RouterLink :to="link.href!">
              <component :is="link.icon"/>
              <span>{{ link.label }}</span>
            </RouterLink>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </div>
    </SidebarMenu>
  </SidebarGroup>
</template>

