<script lang="ts" setup>
import {
  SidebarFooter,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar
} from '@/components/ui/sidebar'
import {BellIcon} from 'lucide-vue-next'
import {Badge} from "@/components/ui/badge";
import SidebarProfile from "@/layout/sidebar/sidebar-profile.vue";

const {setOpenMobile} = useSidebar()
</script>

<template>
  <SidebarFooter>
    <SidebarMenu>
      <SidebarMenuItem>
        <SidebarMenuButton
          as-child
          tooltip="test"
          @click="setOpenMobile(false)"
        >
          <RouterLink to="#">
            <BellIcon/>
            <span>Notificações</span>
            <Badge
              class="ml-auto"
              variant="default"
            >
              1
            </Badge>
          </RouterLink>
        </SidebarMenuButton>
      </SidebarMenuItem>

      <SidebarMenuItem class="mt-1">
        <SidebarProfile/>
      </SidebarMenuItem>
    </SidebarMenu>
  </SidebarFooter>
</template>

