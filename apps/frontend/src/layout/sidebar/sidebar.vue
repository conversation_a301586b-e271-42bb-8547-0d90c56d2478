<script lang="ts" setup>
import {Sidebar, SidebarContent, SidebarInset, SidebarProvider} from "@/components/ui/sidebar";
import SidebarHeader from "./sidebar-header.vue";
import SidebarNavigation from "./sidebar-navigation.vue";
import SidebarFooter from "./sidebar-footer.vue";
import SidebarRail from "./sidebar-rail.vue";
import {useLocalStorage} from "@vueuse/core";

const open = useLocalStorage('coorpeSidebarIsOpen', true)

</script>

<template>
  <SidebarProvider class="min-h-full h-[calc(100vh-3.0rem)]" v-model:open="open"
                   style="--sidebar-width: 14rem; --sidebar-width-mobile: 14rem;"
  >
    <SidebarRail/>
    <Sidebar
      class="left-16 top-12 h-auto"
      collapsible="icon"
    >
      <SidebarHeader/>

      <SidebarContent>
        <SidebarNavigation/>
      </SidebarContent>

      <SidebarFooter/>
    </Sidebar>
    <SidebarInset>
      <slot/>
    </SidebarInset>
  </SidebarProvider>
</template>
