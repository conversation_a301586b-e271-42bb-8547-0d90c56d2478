<script lang="ts" setup>
import {<PERSON><PERSON><PERSON>ead<PERSON>, SidebarTrigger} from "@/components/ui/sidebar";
import {useWorkspaces} from "@/features/workspaces/composables/use-workspaces.ts";

const {currentWorkspace} = useWorkspaces()

</script>

<template>
  <SidebarHeader class="p-4 border-border border-b">
    <div
      class="flex items-center justify-between group-data-[collapsible=icon]:justify-center h-10 pl-2"
    >
      <h1
        class="font-semibold group-data-[collapsible=icon]:hidden text-accent-foreground"
      >
        {{ currentWorkspace?.name }}
      </h1>
      <SidebarTrigger/>
    </div>
  </SidebarHeader>
</template>
