<script lang="ts" setup>
import {But<PERSON>} from "@/components/ui/button";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip";
import {GripIcon} from "lucide-vue-next";
import {Separator} from "@/components/ui/separator";
import WorkspacesDialog from "@/features/workspaces/ui/workspaces-dialog.vue";
import WorkspacesList from "@/features/workspaces/ui/workspaces-list.vue";
</script>

<template>
  <TooltipProvider>
    <div
      class="flex flex-col h-full bg-sidebar-accent border-r border-border py-4">
      <WorkspacesList/>
      <Separator/>
      <div class="p-2 flex flex-col gap-2 items-center">
        <WorkspacesDialog/>

        <Tooltip>
          <TooltipTrigger as-child>
            <Button
              class="size-12 rounded-full"
              size="icon"
              variant="ghost"
            >
              <GripIcon class="size-6"/>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            Integrações
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  </TooltipProvider>
</template>
