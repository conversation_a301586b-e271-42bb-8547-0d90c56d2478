package sessions

import (
	"fmt"
	"github.com/coorpe-app/coorpe/internal/entity"
	"golang.org/x/net/context"
)

func (s *Sessions) GetAuthenticatedUser(ctx context.Context) (entity.User, error) {
	val := s.SessionManager.Get(ctx, "userID")
	userID, ok := val.(string)
	if !ok || userID == "" {
		return entity.Nil, fmt.Errorf("cannot get user id from session")
	}

	user := entity.User{}
	if err := s.db.Table("users").First(&user, "id = ?", userID).Error; err != nil {
		return entity.Nil, fmt.Errorf("cannot get user from db: %w", err)
	}

	return user, nil
}

func (s *Sessions) GetCurrentWorkspaceID(ctx context.Context) (string, error) {
	currentWorkspaceId, ok := s.SessionManager.Get(ctx, "workspaceId").(string)
	if !ok {
		return "", fmt.<PERSON><PERSON><PERSON>("cannot get workspaceId from context")
	}

	return currentWorkspaceId, nil
}

func (s *Sessions) SetCurrentWorkspaceID(ctx context.Context, workspaceId string) error {
	s.SessionManager.Put(ctx, "workspaceId", workspaceId)
	_, _, err := s.SessionManager.Commit(ctx)
	if err != nil {
		return fmt.Errorf("failed to set workspaceId in session: %w", err)
	}

	return nil
}

func (s *Sessions) GetCurrentChannelID(ctx context.Context) (string, error) {
	currentChannelId, ok := s.SessionManager.Get(ctx, "channelId").(string)
	if !ok {
		return "", fmt.Errorf("cannot get channelId from context")
	}

	return currentChannelId, nil
}

func (s *Sessions) SetCurrentChannelID(ctx context.Context, channelId string) error {
	s.SessionManager.Put(ctx, "channelId", channelId)
	_, _, err := s.SessionManager.Commit(ctx)
	if err != nil {
		return fmt.Errorf("failed to set channelId in session: %w", err)
	}

	return nil
}
