package sessions

import (
	"context"
	"encoding/gob"
	"fmt"
	"github.com/alexedwards/scs/goredisstore"
	"github.com/alexedwards/scs/v2"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/libs/config"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/fx"
	"gorm.io/gorm"
)

type Sessions struct {
	SessionManager *scs.SessionManager
	config         config.Config
	db             *gorm.DB
}

type Params struct {
	fx.In

	Redis  *redis.Client
	Config config.Config
	DB     *gorm.DB
}

func New(p Params) *Sessions {
	SessionManager := scs.New()
	SessionManager.Store = goredisstore.New(p.Redis)

	gob.Register(entity.User{})
	return &Sessions{
		SessionManager: SessionManager,
		config:         p.Config,
		db:             p.<PERSON>,
	}
}

func (s *Sessions) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if r := recover(); r != nil {
				fmt.Println(r)
				c.String(500, "Internal Server Error")
			}
		}()

		cookie, err := c.Cookie(s.SessionManager.Cookie.Name)
		if err != nil {
			cookie = ""
		}

		session, err := s.SessionManager.Load(c.Request.Context(), cookie)
		if err != nil {
			s.SessionManager.ErrorFunc(c.Writer, c.Request, err)
			return
		}

		c.Request = c.Request.WithContext(session)

		sessionToken, expiryTime, err := s.SessionManager.Commit(session)
		if err != nil {
			panic(err)
		}

		s.SessionManager.WriteSessionCookie(session, c.Writer, sessionToken, expiryTime)

		c.Next()
	}
}

func (s *Sessions) Put(ctx context.Context, key string, val interface{}) {
	s.SessionManager.Put(ctx, key, val)
	_, _, err := s.SessionManager.Commit(ctx)
	if err != nil {
		panic(err)
	}
}

func (s *Sessions) Destroy(ctx context.Context) error {
	return s.SessionManager.Destroy(ctx)
}
