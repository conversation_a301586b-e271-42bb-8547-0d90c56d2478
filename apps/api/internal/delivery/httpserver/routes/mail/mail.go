package mail

import (
	"context"
	"net/http"

	"github.com/coorpe-app/coorpe/internal/services/mail"
	"github.com/danielgtaylor/huma/v2"
)

func New(api huma.API, service *mail.Service) {
	huma.Register(
		api,
		huma.Operation{
			OperationID: "send-email-code",
			Summary:     "Send email code",
			Description: "Sends an email code to the provided address",
			Method:      http.MethodPost,
			Tags:        []string{"Mail"},
			Path:        "/api/mail/send-email-code",
		}, func(ctx context.Context, i *struct {
			Email string `query:"email" required:"true" example:"<EMAIL>" format:"email"`
		}) (*verifyEmailOutput, error) {
			if err := service.SendEmailCode(ctx, i.Email); err != nil {
				return nil, huma.Error500InternalServerError("failed to send email code", err)
			}

			return &verifyEmailOutput{
				Body: verifyEmailOutputDto{
					Message: "Se o email existir, um código de verificação foi enviado",
				},
			}, nil
		},
	)
	huma.Register(
		api,
		huma.Operation{
			OperationID: "verify-email-code",
			Summary:     "Verify email code",
			Description: "Verifies an email code",
			Method:      http.MethodPost,
			Tags:        []string{"Mail"},
			Path:        "/api/mail/verify-email-code",
		}, func(ctx context.Context, i *struct {
			Code string `query:"code" required:"true" example:"123456"`
		}) (*verifyEmailOutput, error) {
			if err := service.VerifyEmailCode(ctx, i.Code); err != nil {
				return nil, huma.Error500InternalServerError("failed to verify email code", err)
			}

			return &verifyEmailOutput{
				Body: verifyEmailOutputDto{
					Message: "Email verificado com sucesso",
				},
			}, nil
		},
	)
}

type verifyEmailOutput struct {
	Body verifyEmailOutputDto
}

type verifyEmailOutputDto struct {
	Message string `json:"messages" example:"Email verificado com sucesso"`
}
