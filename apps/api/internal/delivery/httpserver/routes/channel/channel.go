package channel

import (
	"context"
	"fmt"
	"github.com/coorpe-app/coorpe/internal/entity"
	"net/http"

	"github.com/coorpe-app/coorpe/internal/services/channel"
	"github.com/danielgtaylor/huma/v2"
	"github.com/google/uuid"
)

func New(Api huma.API, Service *channel.Service) {
	huma.Register(
		Api,
		huma.Operation{
			OperationID: "create-channel",
			Summary:     "Create a channel",
			Description: "Needs authentication",
			Method:      http.MethodPost,
			Tags:        []string{"Channel"},
			Path:        "/api/channel",
			Security: []map[string][]string{
				{"authentication": {}},
			},
		}, func(ctx context.Context, i *struct {
			Body createChannelOutputDto
		}) (*createChannelOutput, error) {
			createInput := channel.CreateChannelInput{
				Name:        i.Body.Name,
				Description: i.Body.Description,
				IsPrivate:   i.Body.IsPrivate,
				WorkspaceID: i.Body.WorkspaceID,
			}

			newChannel, err := Service.Create(ctx, createInput)
			if err != nil {
				return nil, huma.Error500InternalServerError("create channel", err)
			}
			return &createChannelOutput{
				Body: createChannelOutputDto{
					Name:        newChannel.Name,
					Description: newChannel.Description,
					IsPrivate:   newChannel.IsPrivate,
					WorkspaceID: newChannel.WorkspaceID,
				},
			}, nil
		},
	)
	huma.Register(
		Api,
		huma.Operation{
			OperationID: "get-channels",
			Summary:     "Get all channels",
			Description: "Needs authentication",
			Method:      http.MethodGet,
			Tags:        []string{"Channel"},
			Path:        "/api/channels/{workspaceId}",
			Security: []map[string][]string{
				{"authentication": {}},
			},
		}, func(ctx context.Context, input *struct {
			WorkspaceId string `path:"workspaceId" validate:"required"`
			Page        uint   `json:"page" query:"page" example:"1" default:"1" minimum:"1"`
			Limit       uint   `json:"limit" query:"limit" example:"20" default:"20"`
		}) (*getChannelsOutput, error) {
			userID := ctx.Value("user").(entity.User).ID
			if userID == uuid.Nil {
				return nil, fmt.Errorf("usuário não encontrado")
			}

			data, err := Service.GetMany(
				channel.GetManyInput{
					WorkspaceID: input.WorkspaceId,
					Page:        int(input.Page),
					Limit:       int(input.Limit),
					UserID:      userID,
				},
			)
			if err != nil {
				return nil, huma.Error500InternalServerError("error getting channels", err)
			}

			result := make([]channelOutputDto, 0, len(data.Items))
			for _, item := range data.Items {
				result = append(
					result,
					channelOutputDto{
						ID:           item.ID,
						Name:         item.Name,
						Description:  item.Description,
						IsPrivate:    item.IsPrivate,
						WorkspaceID:  item.WorkspaceID,
						MembersCount: item.MembersCount,
					},
				)
			}

			return &getChannelsOutput{
				Body: getChannelsOutputDto{
					Total: int(data.Total),
					Items: result,
				},
			}, nil
		},
	)
}

type createChannelOutput struct {
	Body createChannelOutputDto
}

type createChannelOutputDto struct {
	Name        string  `json:"name" required:"true" minLength:"1" maxLength:"100000" example:"New Channel"`
	Description *string `json:"description" required:"false" nullable:"true" example:"New Channel Description"`
	IsPrivate   bool    `json:"is_private" required:"false" example:"true"`
	WorkspaceID string  `json:"workspace_id" required:"true" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
}

type channelOutputDto struct {
	ID           string  `json:"id" required:"true" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
	Name         string  `json:"name" required:"true" minLength:"1" maxLength:"100000" example:"New Channel"`
	Description  *string `json:"description" required:"false" nullable:"true" example:"New Channel Description"`
	IsPrivate    bool    `json:"is_private" required:"true" example:"true"`
	WorkspaceID  string  `json:"workspace_id" required:"true" example:"kX9pQ"`
	MembersCount int     `json:"members_count" required:"true" example:"5"`
}

type getChannelsOutput struct {
	Body getChannelsOutputDto
}

type getChannelsOutputDto struct {
	Total int                `json:"total" example:"1"`
	Items []channelOutputDto `json:"items"`
}
