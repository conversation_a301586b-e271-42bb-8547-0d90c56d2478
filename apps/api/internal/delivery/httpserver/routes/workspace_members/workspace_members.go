package workspace_members

import (
	"context"
	"github.com/coorpe-app/coorpe/internal/entity"
	"net/http"
	"time"

	"github.com/coorpe-app/coorpe/internal/services/workspace_members"
	"github.com/danielgtaylor/huma/v2"
	"github.com/google/uuid"
)

func New(api huma.API, service *workspace_members.Service) {
	huma.Register(
		api,
		huma.Operation{
			OperationID: "get-workspace-members",
			Summary:     "Get workspace members",
			Description: "Retrieves all members of a workspace",
			Method:      http.MethodGet,
			Tags:        []string{"WorkspaceMembers"},
			Path:        "/api/workspaces/{id}/members",
			Security: []map[string][]string{
				{"authentication": {}},
			},
		}, func(ctx context.Context, input *getWorkspaceMembersInput) (*getWorkspaceMembersOutput, error) {
			userID := ctx.Value("user").(entity.User).ID
			if userID == uuid.Nil {
				return nil, huma.Error401Unauthorized("user not authenticated")
			}

			data, err := service.GetMany(input.ID)
			if err != nil {
				return nil, huma.Error500InternalServerError("failed to get workspace members", err)
			}

			result := make([]workspaceMembersOutputDto, len(data))
			for i, member := range data {
				result[i] = workspaceMembersOutputDto{
					WorkspaceID:   member.WorkspaceID,
					UserID:        member.User.ID,
					UserName:      member.User.Name,
					UserAvatarURL: member.User.AvatarURL,
					UserEmail:     member.User.Email,
					Role:          string(member.Role),
					LastActive:    member.LastActive,
					InvitedBy:     member.InvitedBy,
				}
			}

			return &getWorkspaceMembersOutput{
				Body: getWorkspaceMembersOutputDto{
					Items: result,
					Total: len(result),
				},
			}, nil
		},
	)
}

type getWorkspaceMembersInput struct {
	ID string `path:"id" required:"true" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
}

type workspaceMembersOutputDto struct {
	WorkspaceID   string    `json:"workspace_id" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
	UserID        uuid.UUID `json:"user_id" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
	UserName      string    `json:"user_name" example:"John Doe"`
	UserAvatarURL string    `json:"user_avatar_url" example:"https://example.com/avatar.jpg"`
	UserEmail     string    `json:"user_email" example:"<EMAIL>"`
	Role          string    `json:"role" example:"owner" enum:"owner,admin,member"`
	LastActive    time.Time `json:"last_active" example:"2023-10-01T12:00:00Z"`
	InvitedBy     uuid.UUID `json:"invited_by" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
}

type getWorkspaceMembersOutput struct {
	Body getWorkspaceMembersOutputDto `json:"body"`
}

type getWorkspaceMembersOutputDto struct {
	Items []workspaceMembersOutputDto `json:"items"`
	Total int                         `json:"total"`
}
