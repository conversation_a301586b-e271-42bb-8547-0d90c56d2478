package message

import (
	"context"
	"github.com/gin-gonic/gin"
	"log/slog"
	"net/http"
)

func (c *Messages) handleMessagesWebSocket(ctx *gin.Context) {
	channelID := ctx.Param("channelId")
	if channelID == "" {
		ctx.AbortWithStatus(http.StatusBadRequest)
		return
	}

	ws, err := c.upgrader.Upgrade(ctx.Writer, ctx.Request)
	if err != nil {
		return
	}
	defer ws.Close()

	ctxWithCancel, cancel := context.WithCancel(ctx.Request.Context())
	defer cancel()

	msgCh := c.service.SubscribeToNewMessagesByChannelID(ctxWithCancel, channelID)
	for {
		select {
		case msg, ok := <-msgCh:
			if !ok {
				return
			}
			if err := ws.WriteJSON(msg); err != nil {
				c.logger.Error("Failed to write JSON to WebSocket", slog.Any("error", err))
				return
			}
		case <-ctxWithCancel.Done():
			return
		}
	}
}
