package workspace

import (
	"context"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/internal/services/workspace"
	"github.com/danielgtaylor/huma/v2"
	"github.com/google/uuid"
	"github.com/guregu/null"
	"net/http"
)

func New(api huma.API, service *workspace.Service) {
	huma.Register(
		api,
		huma.Operation{
			OperationID: "create-workspace",
			Summary:     "Create a workspace",
			Description: "Creates a new workspace",
			Method:      http.MethodPost,
			Tags:        []string{"Workspace"},
			Path:        "/api/workspaces",
			Security: []map[string][]string{
				{"authentication": {}},
			},
			MaxBodyBytes: 1024 * 1024 * 5, // 10MB
		}, func(ctx context.Context, input *struct {
			RawBody huma.MultipartFormFiles[struct {
				Icon        huma.FormFile `form:"icon" contentType:"text/plain" required:"false"`
				Name        string        `form:"name"`
				Description string        `form:"description" nullable:"true" required:"false"`
			}]
		}) (*createWorkspaceOutput, error) {
			formData := input.RawBody.Data()

			var url string
			if formData.Icon.IsSet {
				file := formData.Icon

				iconUrl, err := service.CreateIcon(
					ctx, entity.File{
						Content:     file.File,
						Filename:    file.Filename,
						Size:        file.Size,
						ContentType: file.ContentType,
					},
				)
				if err != nil {
					return nil, huma.Error500InternalServerError("failed to upload icon", err)
				}

				url = iconUrl
			}

			data, err := service.Create(
				ctx, workspace.CreateInput{
					Name:        formData.Name,
					Description: null.StringFrom(formData.Description),
					IconURL:     null.StringFrom(url),
				},
			)
			if err != nil {
				return nil, huma.Error500InternalServerError("failed to create workspace", err)
			}

			return &createWorkspaceOutput{
				Body: workspaceOutputDto{
					ID:          data.ID,
					Name:        data.Name,
					Description: data.Description,
					IconURL:     data.IconURL,
				},
			}, nil
		},
	)
	huma.Register(
		api,
		huma.Operation{
			OperationID: "update-workspace",
			Summary:     "Update a workspace",
			Description: "Updates an existing workspace by ID",
			Method:      http.MethodPut,
			Tags:        []string{"Workspace"},
			Path:        "/api/workspaces/{id}",
			Security: []map[string][]string{
				{"authentication": {}},
			},
		}, func(ctx context.Context, input *struct {
			ID   string `path:"id" required:"true" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
			Body struct {
				Name        string  `json:"name" required:"true" example:"Updated Workspace" minLength:"1" maxLength:"100"`
				Description *string `json:"description" required:"false" nullable:"true" example:"This is my updated workspace"`
			}
		}) (*updateWorkspaceOutput, error) {
			updateInput := workspace.UpdateInput{
				Name:        input.Body.Name,
				Description: input.Body.Description,
			}

			data, err := service.UpdateByID(ctx, input.ID, updateInput)
			if err != nil {
				return nil, huma.Error500InternalServerError("failed to update workspace", err)
			}

			return &updateWorkspaceOutput{
				Body: workspaceOutputDto{
					ID:          data.ID,
					Name:        data.Name,
					Description: data.Description,
					IconURL:     data.IconURL,
				},
			}, nil
		},
	)

	huma.Register(
		api,
		huma.Operation{
			OperationID: "get-workspace-by-id",
			Summary:     "Get workspace by id",
			Description: "Retrieves a workspace by its id",
			Method:      http.MethodGet,
			Tags:        []string{"Workspace"},
			Path:        "/api/workspaces/id/{id}",
		}, func(ctx context.Context, input *struct {
			ID string `path:"id" required:"true" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
		}) (*getWorkspaceOutput, error) {
			data, err := service.GetByID(input.ID)
			if err != nil {
				return nil, huma.Error500InternalServerError("failed to get workspace", err)
			}

			return &getWorkspaceOutput{
				Body: workspaceOutputDto{
					ID:          data.ID,
					Name:        data.Name,
					Description: data.Description,
					IconURL:     data.IconURL,
				},
			}, nil
		},
	)
	huma.Register(
		api,
		huma.Operation{
			OperationID: "get-all-workspaces",
			Summary:     "Get all workspaces",
			Description: "Retrieves all workspaces for the authenticated user",
			Method:      http.MethodGet,
			Tags:        []string{"Workspace"},
			Path:        "/api/workspaces",
			Security: []map[string][]string{
				{"authentication": {}},
			},
		}, func(ctx context.Context, input *struct {
			Page  uint `json:"page" query:"page" example:"1" default:"1" minimum:"1"`
			Limit uint `json:"limit" query:"limit" example:"20" default:"20"`
		}) (*getWorkspacesOutput, error) {
			userID := ctx.Value("user").(entity.User).ID
			if userID == uuid.Nil {
				return nil, huma.Error401Unauthorized("user not authenticated")
			}

			data, err := service.GetMany(
				workspace.GetManyInput{
					UserID: userID,
					Page:   int(input.Page),
					Limit:  int(input.Limit),
				},
			)
			if err != nil {
				return nil, huma.Error500InternalServerError("failed to get workspaces", err)
			}

			result := make([]workspaceOutputDto, 0, len(data.Items))
			for _, item := range data.Items {
				result = append(
					result,
					workspaceOutputDto{
						ID:          item.ID,
						Name:        item.Name,
						Description: item.Description,
						IconURL:     item.IconURL,
					},
				)
			}

			return &getWorkspacesOutput{
				Body: getWorkspacesOutputDto{
					Items: result,
					Total: int(data.Total),
				},
			}, nil
		},
	)
	huma.Register(
		api,
		huma.Operation{
			OperationID: "send-workspace-invite",
			Summary:     "Send workspace invite",
			Description: "Sends an invitation to join a workspace",
			Method:      http.MethodPost,
			Tags:        []string{"Workspace"},
			Path:        "/api/workspaces/invite",
			Security: []map[string][]string{
				{"authentication": {}},
			},
		}, func(ctx context.Context, input *struct {
			Body struct {
				Emails      []string `json:"emails" example:"<EMAIL>" format:"email" uniqueItems:"true" maxItems:"10" minItems:"1"`
				WorkspaceID string   `json:"workspace_id" required:"true" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
			}
		}) (*sendInviteOutput, error) {
			user := ctx.Value("user").(entity.User)
			if user.ID == uuid.Nil {
				return nil, huma.Error401Unauthorized("user not authenticated")
			}

			inviteInput := workspace.SendInviteInput{
				WorkspaceID: input.Body.WorkspaceID,
				Emails:      input.Body.Emails,
				UserID:      user.ID,
				UserName:    user.Name,
			}

			data, err := service.SendInvite(ctx, inviteInput)
			if err != nil {
				return nil, huma.Error500InternalServerError("failed to send invite", err)
			}

			return &sendInviteOutput{
				Body: sendInviteOutputDto{
					Sent:     data.Sent,
					Failed:   data.Failed,
					Existing: data.Existing,
				},
			}, nil
		},
	)
}

type createWorkspaceOutput struct {
	Body workspaceOutputDto
}

type updateWorkspaceOutput struct {
	Body workspaceOutputDto
}

type getWorkspaceOutput struct {
	Body workspaceOutputDto
}

type getWorkspacesOutput struct {
	Body getWorkspacesOutputDto
}

type workspaceOutputDto struct {
	ID          string      `json:"id" example:"kX9pQ"`
	Name        string      `json:"name" example:"My Workspace"`
	Description null.String `json:"description" example:"This is my workspace"`
	IconURL     null.String `json:"iconUrl" example:"https://cool.com/cool.png"`
}

type getWorkspacesOutputDto struct {
	Items []workspaceOutputDto `json:"items"`
	Total int                  `json:"total" example:"5"`
}

type sendInviteOutput struct {
	Body sendInviteOutputDto
}

type sendInviteOutputDto struct {
	Sent     []string `json:"sent"`
	Failed   []string `json:"failed"`
	Existing []string `json:"existing"`
}
