package httpserver

import (
	"context"
	"github.com/coorpe-app/coorpe/internal/sessions"
	"log"

	"github.com/coorpe-app/coorpe/libs/config"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"go.uber.org/fx"
)

type Params struct {
	fx.In
	LC      fx.Lifecycle
	Config  config.Config
	Session *sessions.Sessions
}

func New(p Params) *Server {
	g := gin.New()

	g.Use(gin.Logger())
	g.Use(gin.Recovery())

	g.Use(
		cors.New(
			cors.Config{
				AllowAllOrigins:  true,
				AllowMethods:     []string{"*"},
				AllowHeaders:     []string{"*"},
				ExposeHeaders:    []string{"*"},
				AllowCredentials: true,
			},
		),
	)

	g.Use(p.Session.Middleware())

	server := &Server{
		g,
	}

	p.LC.Append(
		fx.Hook{
			OnStart: func(ctx context.Context) error {
				go func() {
					if err := server.Run(":8080"); err != nil {
						log.Fatalf("Server failed to start: %v", err)
					}
				}()
				return nil
			},
			OnStop: func(ctx context.Context) error {
				// Add graceful shutdown logic here if needed
				log.Println("Server stopping")
				return nil
			},
		},
	)
	return server
}

type Server struct {
	*gin.Engine
}
