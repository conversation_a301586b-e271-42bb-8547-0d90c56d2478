package ws

import (
	"github.com/gorilla/websocket"
	"net/http"
)

type Manager struct {
	upgrader *websocket.Upgrader
}

func New() *Manager {
	return &Manager{
		upgrader: &websocket.Upgrader{
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			CheckOrigin: func(r *http.Request) bool {
				return true
			},
		},
	}
}

func (m *Manager) Upgrade(w http.ResponseWriter, r *http.Request) (*websocket.Conn, error) {
	return m.upgrader.Upgrade(w, r, nil)
}
