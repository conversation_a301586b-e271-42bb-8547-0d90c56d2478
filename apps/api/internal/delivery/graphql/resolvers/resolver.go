package resolvers

import (
	natsrouter "github.com/coorpe-app/coorpe/internal/delivery/nats-router"
	"github.com/coorpe-app/coorpe/internal/services/boards"
	"github.com/coorpe-app/coorpe/internal/services/columns"
	"github.com/coorpe-app/coorpe/internal/services/tasks"
	"github.com/coorpe-app/coorpe/internal/sessions"
	"github.com/coorpe-app/coorpe/libs/logger"
	"go.uber.org/fx"
)

type Deps struct {
	fx.In
	Logger   logger.Logger
	Sessions *sessions.Sessions
	Nats     natsrouter.Router

	BoardsService  *boards.Service
	ColumnsService *columns.Service
	TasksService   *tasks.Service
}

type Resolver struct {
	deps Deps
}

func New(deps Deps) (*Resolver, error) {
	return &Resolver{
		deps: deps,
	}, nil
}
