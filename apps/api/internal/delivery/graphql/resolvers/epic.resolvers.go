package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.74

import (
	"context"
	"fmt"

	"github.com/coorpe-app/coorpe/internal/delivery/graphql/gqlmodel"
)

// CreateEpic is the resolver for the createEpic field.
func (r *mutationResolver) CreateEpic(ctx context.Context, input gqlmodel.CreateEpicInput) (*gqlmodel.Epic, error) {
	panic(fmt.Errorf("not implemented: CreateEpic - createEpic"))
}

// UpdateEpic is the resolver for the updateEpic field.
func (r *mutationResolver) UpdateEpic(ctx context.Context, input gqlmodel.UpdateEpicInput) (*gqlmodel.Epic, error) {
	panic(fmt.Errorf("not implemented: UpdateEpic - updateEpic"))
}

// DeleteEpic is the resolver for the deleteEpic field.
func (r *mutationResolver) DeleteEpic(ctx context.Context, id string) (bool, error) {
	panic(fmt.Errorf("not implemented: DeleteEpic - deleteEpic"))
}

// Epic is the resolver for the epic field.
func (r *queryResolver) Epic(ctx context.Context, id string) (*gqlmodel.Epic, error) {
	panic(fmt.Errorf("not implemented: Epic - epic"))
}

// EpicsByWorkspace is the resolver for the epicsByWorkspace field.
func (r *queryResolver) EpicsByWorkspace(ctx context.Context, workspaceID string) ([]gqlmodel.Epic, error) {
	panic(fmt.Errorf("not implemented: EpicsByWorkspace - epicsByWorkspace"))
}

// EpicUpdated is the resolver for the epicUpdated field.
func (r *subscriptionResolver) EpicUpdated(ctx context.Context, boardID string) (<-chan *gqlmodel.Epic, error) {
	panic(fmt.Errorf("not implemented: EpicUpdated - epicUpdated"))
}
