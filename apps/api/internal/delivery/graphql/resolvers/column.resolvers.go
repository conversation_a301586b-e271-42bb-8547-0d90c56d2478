package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.74

import (
	"context"
	"log/slog"

	"github.com/coorpe-app/coorpe/internal/delivery/graphql/gqlmodel"
	"github.com/coorpe-app/coorpe/internal/delivery/graphql/mappers"
	"github.com/coorpe-app/coorpe/internal/services/columns"
	kanban_columns_bus "github.com/coorpe-app/coorpe/libs/bus/columns"
	"github.com/goccy/go-json"
)

// CreateColumn is the resolver for the createColumn field.
func (r *mutationResolver) CreateColumn(ctx context.Context, input gqlmodel.CreateColumnInput) (*gqlmodel.Column, error) {
	column, err := r.deps.ColumnsService.Create(
		columns.CreateInput{
			Title:    input.Title,
			BoardID:  input.BoardID,
			Position: input.Position,
		},
	)
	if err != nil {
		return nil, err
	}

	converted := mappers.ColumnToGraphQL(column)
	return &converted, nil
}

// UpdateColumn is the resolver for the updateColumn field.
func (r *mutationResolver) UpdateColumn(ctx context.Context, input gqlmodel.UpdateColumnInput) (*gqlmodel.Column, error) {
	updatedColumn := columns.UpdateInput{
		Title: input.Title,
	}

	if input.Position.IsSet() {
		updatedColumn.Position = *input.Position.Value()
	}

	column, err := r.deps.ColumnsService.Update(
		input.ID,
		updatedColumn,
	)
	if err != nil {
		return nil, err
	}

	converted := mappers.ColumnToGraphQL(column)
	return &converted, nil
}

// DeleteColumn is the resolver for the deleteColumn field.
func (r *mutationResolver) DeleteColumn(ctx context.Context, id string) (bool, error) {
	err := r.deps.ColumnsService.Delete(id)
	if err != nil {
		return false, err
	}
	return true, nil
}

// Column is the resolver for the column field.
func (r *queryResolver) Column(ctx context.Context, id string) (*gqlmodel.Column, error) {
	column, err := r.deps.ColumnsService.GetByID(id)
	if err != nil {
		return nil, err
	}

	converted := mappers.ColumnToGraphQL(column)
	return &converted, nil
}

// ColumnUpdated is the resolver for the columnUpdated field.
func (r *subscriptionResolver) ColumnUpdated(ctx context.Context, boardID string) (<-chan *gqlmodel.Column, error) {
	channel := make(chan *gqlmodel.Column, 1)

	go func() {
		sub, err := r.deps.Nats.Subscribe(
			[]string{
				columns.CreateUpdatedColumnSubscriptionKeyByBoardID(boardID),
			},
		)
		if err != nil {
			r.deps.Logger.Error("cannot subscribe to kanban column updated", slog.Any("err", err))
			return
		}
		defer func() {
			sub.Unsubscribe()
			close(channel)
		}()

		for {
			select {
			case <-ctx.Done():
				return
			case data := <-sub.GetChannel():
				var column kanban_columns_bus.Column
				if err := json.Unmarshal(data, &column); err != nil {
					r.deps.Logger.Error("cannot unmarshal kanban column", slog.Any("err", err))
					return
				}

				channel <- &gqlmodel.Column{
					Title:    column.Title,
					Position: column.Position,
				}
			}
		}
	}()

	return channel, nil
}
