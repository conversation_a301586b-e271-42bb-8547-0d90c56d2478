package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.74

import (
	"context"
	"fmt"

	"github.com/coorpe-app/coorpe/internal/delivery/graphql/gqlmodel"
)

// CreateStory is the resolver for the createStory field.
func (r *mutationResolver) CreateStory(ctx context.Context, input gqlmodel.CreateStoryInput) (*gqlmodel.Story, error) {
	panic(fmt.Errorf("not implemented: CreateStory - createStory"))
}

// UpdateStory is the resolver for the updateStory field.
func (r *mutationResolver) UpdateStory(ctx context.Context, input gqlmodel.UpdateStoryInput) (*gqlmodel.Story, error) {
	panic(fmt.Errorf("not implemented: UpdateStory - updateStory"))
}

// DeleteStory is the resolver for the deleteStory field.
func (r *mutationResolver) DeleteStory(ctx context.Context, id string) (bool, error) {
	panic(fmt.<PERSON><PERSON>rf("not implemented: DeleteStory - deleteStory"))
}

// StoriesByEpic is the resolver for the storiesByEpic field.
func (r *queryResolver) StoriesByEpic(ctx context.Context, epicID string) ([]gqlmodel.Story, error) {
	panic(fmt.Errorf("not implemented: StoriesByEpic - storiesByEpic"))
}

// StoriesBySprint is the resolver for the storiesBySprint field.
func (r *queryResolver) StoriesBySprint(ctx context.Context, sprintID string) ([]gqlmodel.Story, error) {
	panic(fmt.Errorf("not implemented: StoriesBySprint - storiesBySprint"))
}

// Story is the resolver for the story field.
func (r *queryResolver) Story(ctx context.Context, id string) (*gqlmodel.Story, error) {
	panic(fmt.Errorf("not implemented: Story - story"))
}

// StoryUpdated is the resolver for the storyUpdated field.
func (r *subscriptionResolver) StoryUpdated(ctx context.Context, sprintID string) (<-chan *gqlmodel.Story, error) {
	panic(fmt.Errorf("not implemented: StoryUpdated - storyUpdated"))
}
