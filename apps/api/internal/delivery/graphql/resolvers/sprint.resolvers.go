package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.74

import (
	"context"
	"fmt"

	"github.com/coorpe-app/coorpe/internal/delivery/graphql/gqlmodel"
)

// CreateSprint is the resolver for the createSprint field.
func (r *mutationResolver) CreateSprint(ctx context.Context, input gqlmodel.CreateSprintInput) (*gqlmodel.Sprint, error) {
	panic(fmt.Errorf("not implemented: CreateSprint - createSprint"))
}

// UpdateSprint is the resolver for the updateSprint field.
func (r *mutationResolver) UpdateSprint(ctx context.Context, input gqlmodel.UpdateSprintInput) (*gqlmodel.Sprint, error) {
	panic(fmt.Errorf("not implemented: UpdateSprint - updateSprint"))
}

// DeleteSprint is the resolver for the deleteSprint field.
func (r *mutationResolver) DeleteSprint(ctx context.Context, id string) (bool, error) {
	panic(fmt.Errorf("not implemented: DeleteSprint - deleteSprint"))
}

// Sprint is the resolver for the sprint field.
func (r *queryResolver) Sprint(ctx context.Context, id string) (*gqlmodel.Sprint, error) {
	panic(fmt.Errorf("not implemented: Sprint - sprint"))
}

// SprintsByWorkspace is the resolver for the sprintsByWorkspace field.
func (r *queryResolver) SprintsByWorkspace(ctx context.Context, workspaceID string) ([]gqlmodel.Sprint, error) {
	panic(fmt.Errorf("not implemented: SprintsByWorkspace - sprintsByWorkspace"))
}

// SprintUpdated is the resolver for the sprintUpdated field.
func (r *subscriptionResolver) SprintUpdated(ctx context.Context, workspaceID string) (<-chan *gqlmodel.Sprint, error) {
	panic(fmt.Errorf("not implemented: SprintUpdated - sprintUpdated"))
}
