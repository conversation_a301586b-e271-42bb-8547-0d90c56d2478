package directives

import (
	"context"
	"errors"
	"fmt"
	"github.com/99designs/gqlgen/graphql"
	"github.com/go-playground/locales/pt_BR"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	pt_translations "github.com/go-playground/validator/v10/translations/pt_BR"
	"github.com/vektah/gqlparser/v2/gqlerror"
	"strings"
)

var (
	validate *validator.Validate
	trans    ut.Translator
)

func init() {
	validate = validator.New()
	pt := pt_BR.New()
	uni := ut.New(pt, pt)
	trans, _ = uni.GetTranslator("pt_BR")
	err := pt_translations.RegisterDefaultTranslations(validate, trans)

	if err != nil {
		panic(err)
	}
}

func (d *Directives) Validator(ctx context.Context, obj interface{}, next graphql.Resolver, constraint string) (interface{}, error) {
	val, err := next(ctx)
	if err != nil {
		return nil, err
	}

	pathContext := graphql.GetPathContext(ctx)

	reqMap, ok := obj.(map[string]any)
	if ok && pathContext.Field != nil {
		v, valExists := reqMap[*pathContext.Field]
		if valExists && v == nil && strings.Contains(constraint, "omitempty") {
			return val, nil
		}
	}

	err = validate.Var(val, constraint)

	if err != nil {
		var validationErrors validator.ValidationErrors
		if errors.As(err, &validationErrors) {
			var fieldName string
			if pathContext.Field != nil {
				fieldName = *pathContext.Field
			}

			message := fmt.Sprintf("Validation failed for field '%s'", fieldName)

			extensionValidationErrors := make(map[string]any)
			for _, er := range validationErrors {
				extensionValidationErrors[er.Tag()] = er.Param()
			}

			return nil, &gqlerror.Error{
				Message:   message,
				Path:      pathContext.Path(),
				Locations: nil,
				Extensions: map[string]interface{}{
					"code":              "BAD_REQUEST",
					"validation_errors": extensionValidationErrors,
				},
			}
		}

		return nil, err
	}

	return val, err
}
