extend type Query {
    task(id: ID!): Task @isAuthenticated
    tasksByColumn(columnId: ID!): [Task!]! @isAuthenticated
}

extend type Mutation {
    createTask(input: CreateTaskInput!): Task! @isAuthenticated
    updateTask(input: UpdateTaskInput!): Task! @isAuthenticated
    deleteTask(id: ID!): Boolean! @isAuthenticated
}

extend type Subscription {
    taskUpdated(boardId: ID!): Task! @isAuthenticated
}

type Task {
    id: ID!
    title: String!
    description: String
    status: TaskStatus!
    assigneeId: UUID
    position: Int!
    createdAt: Time!
    updatedAt: Time!
    columnId: ID!
    storyId: ID
}

input CreateTaskInput {
    title: String! @validate(constraint: "min=1,max=255")
    description: String @validate(constraint: "max=500")
    columnId: ID!
    assigneeId: UUID
    storyId: ID # a task can be created without a story
    position: Int!
}

input UpdateTaskInput {
    id: ID!
    title: String @validate(constraint: "min=1,max=255")
    description: String @validate(constraint: "max=500")
    status: TaskStatus
    columnId: ID
    storyId: ID
    assigneeId: UUID
    position: Int
}

enum TaskStatus {
    OPEN
    IN_PROGRESS
    COMPLETED
    CLOSED
}