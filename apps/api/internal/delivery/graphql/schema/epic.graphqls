extend type Query {
    epic(id: ID!): Epic @isAuthenticated
    epicsByWorkspace(workspaceId: ID!): [Epic!]! @isAuthenticated
}

extend type Mutation {
    createEpic(input: CreateEpicInput!): Epic! @isAuthenticated
    updateEpic(input: UpdateEpicInput!): Epic! @isAuthenticated
    deleteEpic(id: ID!): Boolean! @isAuthenticated
}

extend type Subscription {
    epicUpdated(boardId: ID!): Epic! @isAuthenticated
}

type Epic {
    id: ID!
    title: String!
    description: String
    status: EpicStatus!
    priority: Int!
    createdAt: Time!
    updatedAt: Time!
    workspaceId: ID!
    stories: [Story!]!
    assigneeId: UUID
}

enum EpicStatus {
    OPEN
    IN_PROGRESS
    COMPLETED
    CLOSED
}

input CreateEpicInput {
    title: String! @validate(constraint: "min=1,max=255")
    description: String @validate(constraint: "max=500")
    status: EpicStatus!
    priority: Int!
    workspaceId: ID!
    assigneeId: UUID
}

input UpdateEpicInput {
    id: ID!
    title: String @validate(constraint: "min=3,max=255")
    description: String @validate(constraint: "max=500")
    status: EpicStatus
    priority: Int
}
