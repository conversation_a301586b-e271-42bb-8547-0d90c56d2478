extend type Query {
    board(id: ID!): Board @isAuthenticated
    boards(workspaceID: ID!): [Board!]! @isAuthenticated
}

extend type Mutation {
    createBoard(input: CreateBoardInput!): Board! @isAuthenticated
    updateBoard(input: UpdateBoardInput!): Board! @isAuthenticated
    deleteBoard(id: ID!): Boolean! @isAuthenticated
}

extend type Subscription {
    boardUpdated(id: ID!): Board! @isAuthenticated
}

type Board {
    id: ID!
    title: String!
    description: String
    columns: [Column!]! @goField(forceResolver: true)
    workspaceId: String!
    ownerId: UUID!
    createdAt: Time!
    updatedAt: Time!
}

input CreateBoardInput {
    title: String! @validate(constraint: "min=3,max=255")
    description: String @validate(constraint: "max=500")
    workspaceId: ID!
    ownerId: ID!
}

input UpdateBoardInput {
    id: ID!
    title: String!
    description: String
}