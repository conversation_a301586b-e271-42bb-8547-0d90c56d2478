extend type Query {
    column(id: ID!): Column @isAuthenticated
}

extend type Mutation {
    createColumn(input: CreateColumnInput!): Column! @isAuthenticated
    updateColumn(input: UpdateColumnInput!): Column! @isAuthenticated
    deleteColumn(id: ID!): <PERSON><PERSON>an! @isAuthenticated
}

extend type Subscription {
    columnUpdated(boardId: ID!): Column! @isAuthenticated
}

type Column {
    id: ID!
    title: String!
    tasks: [Task!]!
    position: Int!
    boardId: ID!
}

input CreateColumnInput {
    title: String! @validate(constraint: "min=1,max=255")
    boardId: ID!
    position: Int!
}

input UpdateColumnInput {
    id: ID!
    title: String! @validate(constraint: "min=1,max=255")
    position: Int
    boardId: ID
}