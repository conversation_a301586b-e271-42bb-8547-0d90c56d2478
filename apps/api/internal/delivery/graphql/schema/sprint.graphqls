extend type Query {
    sprint(id: ID!): Sprint @isAuthenticated
    sprintsByWorkspace(workspaceId: ID!): [Sprint!]! @isAuthenticated
}

extend type Mutation {
    createSprint(input: CreateSprintInput!): Sprint! @isAuthenticated
    updateSprint(input: UpdateSprintInput!): Sprint! @isAuthenticated
    deleteSprint(id: ID!): Boolean! @isAuthenticated
}

extend type Subscription {
    sprintUpdated(workspaceId: ID!): Sprint! @isAuthenticated
}

type Sprint {
    id: UUID!
    name: String!
    goal: String
    startDate: Time!
    endDate: Time!
    workspaceId: ID!
    stories: [Story!]!
    boards: [Board!]!
    createdAt: Time!
    updatedAt: Time!
}

input CreateSprintInput {
    name: String! @validate(constraint: "min=1,max=255")
    goal: String @validate(constraint: "max=500")
    startDate: Time!
    endDate: Time!
    workspaceId: ID!
}

input UpdateSprintInput {
    id: ID!
    name: String! @validate(constraint: "min=1,max=255")
    goal: String @validate(constraint: "max=500")
    startDate: Time
    endDate: Time
}