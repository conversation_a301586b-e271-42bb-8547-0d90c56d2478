package graphql

import (
	"net/http"
	"time"

	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/handler/extension"
	"github.com/99designs/gqlgen/graphql/handler/transport"
	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/coorpe-app/coorpe/internal/delivery/graphql/directives"
	"github.com/coorpe-app/coorpe/internal/delivery/graphql/graph"
	"github.com/coorpe-app/coorpe/internal/delivery/graphql/resolvers"
	"github.com/coorpe-app/coorpe/internal/delivery/httpserver"
	"github.com/coorpe-app/coorpe/libs/config"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/fx"
)

type Gql struct {
	*handler.Server
}

type Params struct {
	fx.In

	Server     *httpserver.Server
	Resolver   *resolvers.Resolver
	Directives *directives.Directives
	Config     config.Config
}

func New(p Params) *Gql {
	graphConfig := graph.Config{
		Resolvers: p.Resolver,
	}

	graphConfig.Directives.IsAuthenticated = p.Directives.IsAuthenticated
	graphConfig.Directives.Validate = p.Directives.Validator

	srv := handler.New(graph.NewExecutableSchema(graphConfig))

	srv.AddTransport(transport.Options{})
	srv.AddTransport(transport.GET{})
	srv.AddTransport(transport.POST{})
	srv.AddTransport(transport.MultipartForm{})
	srv.AddTransport(
		transport.Websocket{
			KeepAlivePingInterval: 10 * time.Second,
			Upgrader: websocket.Upgrader{
				CheckOrigin: func(r *http.Request) bool {
					return true
				},
			},
			MissingPongOk: false,
		},
	)

	if p.Config.AppEnv != "production" {
		srv.Use(extension.Introspection{})
	}

	playgroundHandler := playground.Handler("GraphQL", "/query")
	p.Server.Any(
		"/", func(c *gin.Context) {
			playgroundHandler.ServeHTTP(c.Writer, c.Request)
		},
	)

	p.Server.Any(
		"/query",
		func(c *gin.Context) {
			srv.ServeHTTP(c.Writer, c.Request)
		},
	)

	return &Gql{srv}
}
