package mappers

import (
	"github.com/coorpe-app/coorpe/internal/delivery/graphql/gqlmodel"
	"github.com/coorpe-app/coorpe/internal/entity"
)

func BoardToGraphQL(board entity.Board) gqlmodel.Board {
	return gqlmodel.Board{
		ID:          board.ID,
		Title:       board.Title,
		Description: board.Description,
		OwnerID:     board.OwnerID,
		CreatedAt:   board.CreatedAt,
		UpdatedAt:   board.UpdatedAt,
		Columns:     ColumnsToGraphQL(board.Columns),
	}
}

func ColumnToGraphQL(column entity.Column) gqlmodel.Column {
	return gqlmodel.Column{
		ID:       column.ID,
		Title:    column.Title,
		Position: column.Position,
		BoardID:  column.BoardID,
		Tasks:    TasksToGraphQL(column.Tasks),
	}
}

func ColumnsToGraphQL(columns []entity.Column) []gqlmodel.Column {
	var gqlColumns []gqlmodel.Column
	for _, column := range columns {
		gqlColumns = append(
			gqlColumns, gqlmodel.Column{
				ID:       column.ID,
				Title:    column.Title,
				Position: column.Position,
				BoardID:  column.BoardID,
				Tasks:    TasksToGraphQL(column.Tasks),
			},
		)
	}
	return gqlColumns
}

func TaskToGraphQL(task entity.Task) gqlmodel.Task {
	return gqlmodel.Task{
		ID:          task.ID,
		Title:       task.Title,
		Description: task.Description,
		Status:      gqlmodel.TaskStatus(task.Status),
		ColumnID:    task.ColumnID,
		AssigneeID:  task.AssigneeID,
		CreatedAt:   task.CreatedAt,
		UpdatedAt:   task.UpdatedAt,
	}
}

func TasksToGraphQL(tasks []entity.Task) []gqlmodel.Task {
	var gqlTasks []gqlmodel.Task
	for _, task := range tasks {
		gqlTasks = append(
			gqlTasks, gqlmodel.Task{
				ID:          task.ID,
				Title:       task.Title,
				Description: task.Description,
				Status:      gqlmodel.TaskStatus(task.Status),
				ColumnID:    task.ColumnID,
				AssigneeID:  task.AssigneeID,
				CreatedAt:   task.CreatedAt,
				UpdatedAt:   task.UpdatedAt,
			},
		)
	}
	return gqlTasks
}
