package nats_router

import (
	"github.com/goccy/go-json"
	"github.com/nats-io/nats.go"
)

func NewNatsSubscription(p Params) (*RouterNats, error) {
	nc, err := nats.Connect(p.Config.NatsURL)
	if err != nil {
		return nil, err
	}

	return &RouterNats{
		nc: nc,
	}, nil
}

type RouterNats struct {
	nc *nats.Conn
}

var _ Router = &RouterNats{}

type RouterNatsSubscription struct {
	subs      []*nats.Subscription
	dataChann chan []byte
}

func (c *RouterNatsSubscription) GetChannel() chan []byte {
	return c.dataChann
}

func (c *RouterNatsSubscription) Unsubscribe() error {
	for _, sub := range c.subs {
		if err := sub.Unsubscribe(); err != nil {
			panic(err)
		}
	}

	return nil
}

func (r *RouterNats) Subscribe(keys []string) (Subscription, error) {
	ch := make(chan []byte, 1)
	subs := make([]*nats.Subscription, 0, len(keys))

	for _, key := range keys {
		sub, err := r.nc.Subscribe(
			key,
			func(msg *nats.Msg) {
				ch <- msg.Data
			},
		)

		if err != nil {
			return nil, err
		}

		subs = append(subs, sub)
	}

	return &RouterNatsSubscription{
		subs:      subs,
		dataChann: ch,
	}, nil
}

func (r *RouterNats) Publish(key string, data any) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	return r.nc.Publish(key, dataBytes)
}
