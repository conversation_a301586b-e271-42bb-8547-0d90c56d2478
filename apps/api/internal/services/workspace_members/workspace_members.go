package workspace_members

import (
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/libs/repositories/workspace_members"
	"github.com/coorpe-app/coorpe/libs/repositories/workspace_members/model"
	"gorm.io/gorm"
)

type Service struct {
	DB *gorm.DB

	repo workspace_members.Repository
}

func New(
	DB *gorm.DB,
	repo workspace_members.Repository,
) *Service {
	return &Service{
		DB:   DB,
		repo: repo,
	}
}

func (s *Service) modelToEntity(m model.WorkspaceMember) entity.WorkspaceMember {
	return entity.WorkspaceMember{
		Role:       entity.WorkspaceMemberRole(m.Role),
		LastActive: m.LastActive,
		InvitedBy:  m.InvitedBy,
		User: entity.User{
			ID:        m.UserID,
			Name:      m.UserName,
			AvatarURL: m.UserAvatarURL,
			Email:     m.UserEmail,
		},
	}
}

func (s *Service) GetMany(workspaceID string) ([]entity.WorkspaceMember, error) {
	members, err := s.repo.GetMany(workspaceID)
	if err != nil {
		return nil, err
	}

	entities := make([]entity.WorkspaceMember, len(members))
	for i, m := range members {
		entities[i] = s.modelToEntity(m)
	}

	return entities, nil
}
