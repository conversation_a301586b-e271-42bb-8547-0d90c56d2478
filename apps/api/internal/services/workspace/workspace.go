package workspace

import (
	"context"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/internal/mailer"
	"github.com/coorpe-app/coorpe/libs/logger"
	"github.com/coorpe-app/coorpe/libs/repositories/workspace/model"
	"github.com/danielgtaylor/huma/v2"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"github.com/redis/go-redis/v9"
	"go.uber.org/fx"
	"time"

	"github.com/coorpe-app/coorpe/libs/config"
	workspaces "github.com/coorpe-app/coorpe/libs/repositories/workspace"
	members "github.com/coorpe-app/coorpe/libs/repositories/workspace_members"
	"github.com/google/uuid"
	"github.com/guregu/null"
	"github.com/minio/minio-go/v7"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

type Params struct {
	fx.In

	DB            *gorm.DB
	Logger        logger.Logger
	Redis         *redis.Client
	Mailer        *mailer.Mailer
	WorkspaceRepo workspaces.Repository
	MemberRepo    members.Repository
	MinioClient   *minio.Client
	Config        config.Config
}

func New(p Params) *Service {
	return &Service{
		DB:            p.DB,
		Logger:        p.Logger,
		redis:         p.Redis,
		mailer:        p.Mailer,
		workspaceRepo: p.WorkspaceRepo,
		memberRepo:    p.MemberRepo,
		minioClient:   p.MinioClient,
		config:        p.Config,
	}
}

type Service struct {
	DB *gorm.DB

	Logger        logger.Logger
	redis         *redis.Client
	mailer        *mailer.Mailer
	workspaceRepo workspaces.Repository
	memberRepo    members.Repository
	minioClient   *minio.Client
	config        config.Config
}

func (s *Service) modelToEntity(m model.Workspace) *entity.Workspace {
	return &entity.Workspace{
		ID:          m.ID,
		Name:        m.Name,
		Description: m.Description,
		IconURL:     m.IconURL,
	}
}

type CreateInput struct {
	Name        string
	Description null.String
	IconURL     null.String
}

func (s *Service) generateID() string {
	return gonanoid.Must(10)
}

func (s *Service) Create(c context.Context, input CreateInput) (*entity.Workspace, error) {
	tx := s.DB.WithContext(c).Begin()
	defer tx.Rollback()

	user, ok := c.Value("user").(entity.User)
	if !ok {
		return nil, huma.Error401Unauthorized("User not authenticated")
	}

	result, err := s.workspaceRepo.Create(
		tx, workspaces.CreateInput{
			ID:          s.generateID(),
			Name:        input.Name,
			Description: input.Description,
			IconURL:     input.IconURL,
		},
	)
	if err != nil {
		return nil, err
	}

	if err := tx.Create(
		&entity.WorkspaceMember{
			WorkspaceID: result.ID,
			UserID:      user.ID,
			InvitedBy:   user.ID,
			Role:        entity.Owner,
			LastActive:  time.Now(),
		},
	).Error; err != nil {
		return nil, err
	}

	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	return s.modelToEntity(result), nil
}

type UpdateInput struct {
	Name        string
	Description *string
	IconURL     *string
}

func (s *Service) UpdateByID(ctx context.Context, id string, input UpdateInput) (*entity.Workspace, error) {
	workspace, err := s.workspaceRepo.GetByID(id)
	if err != nil {
		return nil, ErrWorkspaceNotFound
	}

	userID := ctx.Value("user").(entity.User).ID
	if userID == uuid.Nil {
		return nil, ErrUserNotFound
	}

	role, err := s.memberRepo.GetRole(id, userID)
	if err != nil || !lo.Contains(
		[]entity.WorkspaceMemberRole{entity.Owner, entity.Admin},
		entity.WorkspaceMemberRole(role),
	) {
		return nil, ErrNotAllowed
	}

	workspace.Name = input.Name
	workspace.Description = null.StringFromPtr(input.Description)
	workspace.IconURL = null.StringFromPtr(input.IconURL)

	if err := s.workspaceRepo.Update(workspace); err != nil {
		return nil, err
	}

	return s.modelToEntity(workspace), nil
}

type GetManyInput struct {
	UserID uuid.UUID
	Page   int
	Limit  int
}

type GetManyOutput struct {
	Items []model.Workspace
	Total int64
}

func (s *Service) GetByID(id string) (*entity.Workspace, error) {
	workspace, err := s.workspaceRepo.GetByID(id)
	if err != nil {
		return nil, ErrWorkspaceNotFound
	}

	return s.modelToEntity(workspace), nil
}

func (s *Service) GetMany(input GetManyInput) (GetManyOutput, error) {
	result, err := s.workspaceRepo.GetMany(
		workspaces.GetManyInput{
			UserID: input.UserID,
			Page:   input.Page,
			Limit:  input.Limit,
		},
	)

	if err != nil {
		return GetManyOutput{}, ErrNoWorkspacesFound
	}

	output := GetManyOutput{
		Items: make([]model.Workspace, 0, len(result.Items)),
		Total: result.Total,
	}

	for _, w := range result.Items {
		output.Items = append(output.Items, w)
	}

	return output, nil
}
