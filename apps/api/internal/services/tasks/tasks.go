package tasks

import (
	"errors"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/libs/bus"
	kanban_tasks_bus "github.com/coorpe-app/coorpe/libs/bus/tasks"
	"github.com/coorpe-app/coorpe/libs/logger"
	"github.com/coorpe-app/coorpe/libs/repositories/tasks"
	"github.com/coorpe-app/coorpe/libs/repositories/tasks/model"
	"github.com/coorpe-app/coorpe/pkg/typeid"
	"github.com/google/uuid"
	"go.uber.org/fx"
	"golang.org/x/exp/slog"
)

type Params struct {
	fx.In

	Bus    *bus.Bus
	Logger logger.Logger
	Repo   tasks.Repository
}

func New(p Params) *Service {
	return &Service{
		bus:    p.Bus,
		repo:   p.Repo,
		logger: p.Logger,
	}
}

type Service struct {
	bus    *bus.Bus
	repo   tasks.Repository
	logger logger.Logger
}

func (s *Service) modelToEntity(task model.Task) entity.Task {
	return entity.Task{
		ID:          task.ID,
		Title:       task.Title,
		Description: task.Description,
		Status:      entity.TaskStatusEnum(task.Status.String()),
		ColumnID:    task.ColumnID,
		AssigneeID:  task.AssigneeID,
		Position:    task.Position,
		CreatedAt:   task.CreatedAt,
		UpdatedAt:   task.UpdatedAt,
	}
}

func (s *Service) GetMany(columnID string) ([]entity.Task, error) {
	dbTasks, err := s.repo.GetMany(columnID)
	if err != nil {
		s.logger.Error("cannot get kanban tasks", slog.Any("error", err))
		return nil, err
	}

	var entities []entity.Task
	for _, task := range dbTasks {
		entities = append(entities, s.modelToEntity(task))
	}
	return entities, nil
}

func (s *Service) GetByColumnID(columnID string) ([]entity.Task, error) {
	dbTasks, err := s.repo.GetByColumnID(columnID)
	if err != nil {
		s.logger.Error("cannot get kanban tasks", slog.Any("error", err))
		return nil, err
	}

	var entities []entity.Task
	for _, task := range dbTasks {
		entities = append(entities, s.modelToEntity(task))
	}
	return entities, nil
}

func (s *Service) GetByID(id string) (entity.Task, error) {
	task, err := s.repo.GetByID(id)
	if err != nil {
		s.logger.Error("cannot get kanban task", slog.Any("error", err))
		return entity.TaskNil, err
	}

	return s.modelToEntity(task), nil
}

type CreateInput struct {
	Title       string
	Description *string
	Status      entity.TaskStatusEnum
	ColumnID    string
	AssigneeID  *uuid.UUID
	Position    int32
}

func (s *Service) Create(input CreateInput) (entity.Task, error) {
	createdTask, err := s.repo.Create(
		tasks.CreateInput{
			ID:          typeid.GenerateID("task"),
			Title:       input.Title,
			Description: input.Description,
			Status:      model.TaskStatusEnum(input.Status),
			ColumnID:    input.ColumnID,
			AssigneeID:  input.AssigneeID,
			Position:    input.Position,
		},
	)
	if err != nil {
		s.logger.Error("err", slog.Any("error", err))
		return entity.TaskNil, err
	}

	return s.modelToEntity(createdTask), nil
}

type UpdateInput struct {
	Title       string
	Description *string
	Status      entity.TaskStatusEnum
	ColumnID    string
	AssigneeID  *uuid.UUID
	Position    int32
}

func (s *Service) Update(id string, input UpdateInput) (entity.Task, error) {
	dbTask, err := s.repo.GetByID(id)
	if err != nil {
		s.logger.Error("cannot get kanban task", slog.Any("error", err))
		return entity.TaskNil, err
	}

	if dbTask.ColumnID != input.ColumnID {
		return entity.TaskNil, errors.New("column doesnt belong to task")
	}

	updatedTask, err := s.repo.Update(
		tasks.UpdateInput{
			Title:       input.Title,
			Description: input.Description,
			Status:      model.TaskStatusEnum(input.Status.String()),
			ColumnID:    input.ColumnID,
			AssigneeID:  input.AssigneeID,
			Position:    input.Position,
		},
	)
	if err != nil {
		s.logger.Error("cannot update kanban task", slog.Any("error", err))
		return entity.TaskNil, err
	}

	if err := s.bus.Kanban.UpdateTask.Publish(
		kanban_tasks_bus.Task{
			Title:       updatedTask.Title,
			Description: updatedTask.Description,
			Status:      string(updatedTask.Status),
			ColumnID:    updatedTask.ColumnID,
			AssigneeID:  updatedTask.AssigneeID,
			Position:    updatedTask.Position,
			UpdatedAt:   updatedTask.UpdatedAt,
		},
	); err != nil {
		s.logger.Error("cannot publish kanban task updated", slog.Any("error", err))
		return entity.TaskNil, err
	}

	return s.modelToEntity(updatedTask), nil
}

func (s *Service) Delete(id string) error {
	err := s.repo.Delete(id)
	if err != nil {
		s.logger.Error("cannot delete kanban task", slog.Any("error", err))
		return err
	}

	return nil
}
