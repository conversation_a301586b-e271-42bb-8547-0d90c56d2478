package columns

import (
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/libs/bus"
	kanban_columns_bus "github.com/coorpe-app/coorpe/libs/bus/columns"
	"github.com/coorpe-app/coorpe/libs/logger"
	"github.com/coorpe-app/coorpe/libs/repositories/columns"
	"github.com/coorpe-app/coorpe/libs/repositories/columns/model"
	"github.com/coorpe-app/coorpe/pkg/typeid"
	"go.uber.org/fx"
	"log/slog"
)

type Params struct {
	fx.In

	Bus    *bus.Bus
	Logger logger.Logger
	Repo   columns.Repository
}

func New(p Params) *Service {
	return &Service{
		bus:    p.Bus,
		repo:   p.Repo,
		logger: p.Logger,
	}
}

type Service struct {
	bus    *bus.Bus
	repo   columns.Repository
	logger logger.Logger
}

func (s *Service) kanbanColumnsModelToEntity(column model.Column) entity.Column {
	return entity.Column{
		ID:       column.ID,
		Title:    column.Title,
		BoardID:  column.BoardID,
		Position: column.Position,
	}
}

func (s *Service) GetMany(boardID string) ([]entity.Column, error) {
	dbColumns, err := s.repo.GetMany(boardID)
	if err != nil {
		s.logger.Error("err", slog.Any("error", err))
		return nil, err
	}

	var entities []entity.Column
	for _, column := range dbColumns {
		entities = append(entities, s.kanbanColumnsModelToEntity(column))
	}

	return entities, nil
}

func (s *Service) GetByID(id string) (entity.Column, error) {
	column, err := s.repo.GetByID(id)
	if err != nil {
		s.logger.Error("err", slog.Any("error", err))
		return entity.ColumnNil, err
	}

	return s.kanbanColumnsModelToEntity(column), nil
}

type CreateInput struct {
	Title    string
	BoardID  string
	Position int32
}

func (s *Service) Create(input CreateInput) (entity.Column, error) {
	modelColumn := model.Column{
		ID:       typeid.GenerateID("column"),
		Title:    input.Title,
		BoardID:  input.BoardID,
		Position: input.Position,
	}

	createdColumn, err := s.repo.Create(modelColumn)
	if err != nil {
		s.logger.Error("cannot create kanban column", slog.Any("error", err))
		return entity.ColumnNil, err
	}

	return s.kanbanColumnsModelToEntity(createdColumn), nil
}

type UpdateInput struct {
	Title    string
	Position int32
}

func (s *Service) Update(id string, input UpdateInput) (entity.Column, error) {
	column, err := s.repo.GetByID(id)
	if err != nil {
		s.logger.Error("cannot get kanban column", slog.Any("error", err))
		return entity.ColumnNil, err
	}

	column.Title = input.Title
	column.Position = input.Position

	updatedColumn, err := s.repo.Update(column)
	if err != nil {
		s.logger.Error("cannot update kanban column", slog.Any("error", err))
		return entity.ColumnNil, err
	}

	if err := s.bus.Kanban.UpdateColumn.Publish(
		kanban_columns_bus.Column{
			Title:    updatedColumn.Title,
			Position: updatedColumn.Position,
		},
	); err != nil {
		s.logger.Error("cannot publish kanban column updated", slog.Any("error", err))
		return entity.ColumnNil, err
	}

	return s.kanbanColumnsModelToEntity(updatedColumn), nil
}

func (s *Service) Delete(id string) error {
	err := s.repo.Delete(id)
	if err != nil {
		s.logger.Error("cannot delete kanban column", slog.Any("error", err))
		return err
	}

	return nil
}
