package channel

import (
	"context"
	"fmt"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/libs/repositories/channel"
	channelModel "github.com/coorpe-app/coorpe/libs/repositories/channel/model"
	"github.com/google/uuid"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"gorm.io/gorm"
)

type Service struct {
	DB *gorm.DB

	channelRepo channel.Repository
}

func New(
	DB *gorm.DB,
	channelRepo channel.Repository,
) *Service {
	return &Service{
		DB:          DB,
		channelRepo: channelRepo,
	}
}

func (s *Service) modelToEntity(m channelModel.Channel) entity.Channel {
	return entity.Channel{
		ID:          m.ID,
		Name:        m.Name,
		Description: m.Description,
		IsPrivate:   m.IsPrivate,
		WorkspaceID: m.WorkspaceID,
	}
}

type CreateChannelInput struct {
	Name        string
	Description *string
	IsPrivate   bool
	WorkspaceID string
}

func (s *Service) generateID() string {
	return gonanoid.Must(10)
}

func (s *Service) Create(c context.Context, input CreateChannelInput) (entity.Channel, error) {
	tx := s.DB.WithContext(c).Begin()
	defer tx.Rollback()

	userID := c.Value("user").(entity.User).ID
	if userID == uuid.Nil {
		return entity.ChannelNil, fmt.Errorf("usuário não encontrado")
	}

	newChannel, err := s.channelRepo.Create(
		tx, &channel.CreateInput{
			ID:          s.generateID(),
			Name:        input.Name,
			Description: input.Description,
			IsPrivate:   input.IsPrivate,
			WorkspaceID: input.WorkspaceID,
		},
	)

	if err != nil {
		return entity.ChannelNil, err
	}

	if err := tx.Create(
		&entity.ChannelMember{
			ChannelID: newChannel.ID,
			UserID:    userID,
		},
	).Error; err != nil {
		return entity.ChannelNil, err
	}

	if err := tx.Commit().Error; err != nil {
		return entity.ChannelNil, err
	}

	return s.modelToEntity(newChannel), nil
}

type GetManyInput struct {
	Page        int
	Limit       int
	WorkspaceID string
	UserID      uuid.UUID
}

type GetManyOutput struct {
	Items []channelModel.Channel
	Total int64
}

func (s *Service) GetMany(input GetManyInput) (GetManyOutput, error) {
	channels, err := s.channelRepo.GetMany(
		channel.GetManyInput{
			Page:        input.Page,
			Limit:       input.Limit,
			WorkspaceID: input.WorkspaceID,
			UserID:      input.UserID,
		},
	)

	if err != nil {
		return GetManyOutput{}, err
	}

	output := GetManyOutput{
		Items: make([]channelModel.Channel, 0, len(channels.Items)),
		Total: channels.Total,
	}

	for _, ch := range channels.Items {
		output.Items = append(
			output.Items, ch,
		)
	}

	return output, nil
}
