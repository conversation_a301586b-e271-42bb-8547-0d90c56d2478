package auth

import (
	"context"
	"errors"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/libs/logger"
	"github.com/coorpe-app/coorpe/libs/repositories/user"
	"github.com/coorpe-app/coorpe/libs/repositories/user/model"
	"go.uber.org/fx"
	"log/slog"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type Params struct {
	fx.In

	Logger logger.Logger
	Redis  *redis.Client
	Repo   user.Repository
}

func New(p Params) *Service {
	return &Service{
		repo:   p.Repo,
		Logger: p.Logger,
		redis:  p.Redis,
	}
}

type Service struct {
	redis  *redis.Client
	repo   user.Repository
	Logger logger.Logger
}

type RegisterInput struct {
	Name     string
	Email    string
	Password string
	Birthday time.Time
}

func (s *Service) modelToEntity(model model.User) entity.User {
	return entity.User{
		ID:            model.ID,
		Name:          model.Name,
		Email:         model.Email,
		Birthday:      model.Birthday,
		AvatarURL:     model.AvatarURL,
		EmailVerified: model.EmailVerified,
		CreatedAt:     model.CreatedAt,
	}
}

func (s *Service) Register(_ context.Context, input RegisterInput) (entity.User, error) {
	exists, err := s.repo.ExistsByEmail(input.Email)
	if exists {
		return entity.Nil, errors.New("email já cadastrado")
	}

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return entity.Nil, err
	}

	password, err := bcrypt.GenerateFromPassword([]byte(input.Password), bcrypt.DefaultCost)
	if err != nil {
		return entity.Nil, err
	}

	avatarURL := GetRandomUserIcon()

	newUser, err := s.repo.Create(
		user.CreateInput{
			ID:            uuid.New(),
			Name:          input.Name,
			Email:         input.Email,
			Password:      string(password),
			Birthday:      input.Birthday,
			AvatarURL:     avatarURL,
			EmailVerified: true,
		},
	)
	if err != nil {
		s.Logger.Error("failed to create user", slog.Any("err", err))
		return entity.Nil, err
	}

	return s.modelToEntity(newUser), nil
}

type LoginInput struct {
	Email    string
	Password string
}

func (s *Service) Login(input LoginInput) (uuid.UUID, error) {
	foundUser, err := s.repo.GetByEmail(input.Email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			s.Logger.Error("user not found", slog.Any("err", err))
		}
		return uuid.Nil, errors.New("email ou senha inválidos")
	}

	if err := bcrypt.CompareHashAndPassword([]byte(foundUser.Password), []byte(input.Password)); err != nil {
		return uuid.Nil, errors.New("email ou senha inválidos")
	}

	if !foundUser.EmailVerified {
		return uuid.Nil, errors.New("email não verificado")
	}

	return foundUser.ID, nil
}
