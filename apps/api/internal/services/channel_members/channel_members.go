package channel_members

import (
	"github.com/coorpe-app/coorpe/libs/repositories/channel_members"
	"github.com/coorpe-app/coorpe/libs/repositories/channel_members/model"
	"go.uber.org/fx"
)

type Params struct {
	fx.In

	Repo channel_members.Repository
}

func New(p Params) *Service {
	return &Service{
		repo: p.Repo,
	}
}

type Service struct {
	repo channel_members.Repository
}

func (s *Service) GetMany(channelID string) ([]model.ChannelMember, error) {
	channelMembers, err := s.repo.GetMany(channelID)
	if err != nil {
		return nil, err
	}

	return channelMembers, nil
}
