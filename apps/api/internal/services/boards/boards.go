package boards

import (
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/libs/bus"
	kanban_boards_bus "github.com/coorpe-app/coorpe/libs/bus/boards"
	"github.com/coorpe-app/coorpe/libs/logger"
	"github.com/coorpe-app/coorpe/libs/repositories/boards"
	"github.com/coorpe-app/coorpe/libs/repositories/boards/model"
	"github.com/coorpe-app/coorpe/pkg/typeid"
	"github.com/google/uuid"
	"go.uber.org/fx"
	"log/slog"
)

type Service struct {
	repo   boards.Repository
	logger logger.Logger
	bus    *bus.Bus
}

type Params struct {
	fx.In

	Bus    *bus.Bus
	Logger logger.Logger
	Repo   boards.Repository
}

func New(p Params) *Service {
	return &Service{
		repo:   p.Repo,
		logger: p.<PERSON>gger,
		bus:    p.Bus,
	}
}

func (s *Service) kanbanBoardsModelToEntity(board model.Board) entity.Board {
	return entity.Board{
		ID:          board.ID,
		Title:       board.Title,
		Description: board.Description,
		CreatedAt:   board.CreatedAt,
		UpdatedAt:   board.UpdatedAt,
	}
}

func (s *Service) GetMany(workspaceID string) ([]entity.Board, error) {
	dbBoards, err := s.repo.GetMany(workspaceID)
	if err != nil {
		s.logger.Error("err", slog.Any("error", err))
		return nil, err
	}

	var entities []entity.Board
	for _, board := range dbBoards {
		entities = append(entities, s.kanbanBoardsModelToEntity(board))
	}

	return entities, nil
}

func (s *Service) GetByID(id string) (entity.Board, error) {
	board, err := s.repo.GetByID(id)
	if err != nil {
		s.logger.Error("err", slog.Any("error", err))
		return entity.Board{}, err
	}

	return s.kanbanBoardsModelToEntity(board), nil
}

type CreateInput struct {
	Title       string
	Description *string
	WorkspaceID string
	OwnerID     uuid.UUID
}

func (s *Service) Create(input CreateInput) (entity.Board, error) {
	modelBoard := model.Board{
		ID:          typeid.GenerateID("board"),
		Title:       input.Title,
		Description: input.Description,
		OwnerID:     input.OwnerID,
		WorkspaceID: input.WorkspaceID,
	}

	createdBoard, err := s.repo.Create(modelBoard)
	if err != nil {
		s.logger.Error("create kanban board", slog.Any("error", err))
		return entity.Board{}, err
	}

	return s.kanbanBoardsModelToEntity(createdBoard), nil
}

type UpdateInput struct {
	Title       string
	Description *string
}

func (s *Service) Update(id string, input UpdateInput) (entity.Board, error) {
	board, err := s.repo.GetByID(id)
	if err != nil {
		return entity.BoardNil, err
	}

	board.Title = input.Title
	board.Description = input.Description
	updatedBoard, err := s.repo.Update(board)
	if err != nil {
		s.logger.Error("cannot update kanban board", slog.Any("error", err))
		return entity.BoardNil, err
	}

	if err := s.bus.Kanban.UpdateBoard.Publish(
		kanban_boards_bus.Board{
			Title:       updatedBoard.Title,
			Description: updatedBoard.Description,
			OwnerID:     updatedBoard.OwnerID,
			UpdatedAt:   updatedBoard.UpdatedAt,
		},
	); err != nil {
		s.logger.Error("cannot publish kanban board updated", slog.Any("error", err))
	}

	return s.kanbanBoardsModelToEntity(updatedBoard), nil
}

func (s *Service) Delete(id string) error {
	err := s.repo.Delete(id)
	if err != nil {
		s.logger.Error("cannot delete kanban board", slog.Any("error", err))
		return err
	}
	return nil
}
