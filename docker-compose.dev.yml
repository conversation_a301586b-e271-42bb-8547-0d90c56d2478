services:
  postgres:
    image: postgres:17
    environment:
      POSTGRES_USER: coorpe
      POSTGRES_PASSWORD: coorpe
      POSTGRES_DB: coorpe
    ports:
      - "5432:5432"
    volumes:
      - postgres:/var/lib/postgresql/data
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U coorpe" ]
      interval: 10s
      timeout: 5s
      retries: 5

  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minio
      MINIO_ROOT_PASSWORD: minio
    command: server /data

  redis:
    image: redis:latest
    ports:
      - "6379:6379"

  nats:
    image: nats:2.11.4-scratch
    command: "-js -m 8222"
    restart: unless-stopped
    ports:
      - "4222:4222"
      - "8222:8222"

volumes:
  postgres: